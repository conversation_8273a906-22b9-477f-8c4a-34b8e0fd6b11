{"version": 3, "file": "no-misleading-array-reverse.js", "sourceRoot": "", "sources": ["../../src/rules/no-misleading-array-reverse.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAKjD,+CAAiC;AACjC,oCAOkB;AAElB,MAAM,oBAAoB,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,gBAAQ,CAAC,CAAC;AAEnE,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QACD,OAAO;YACL,cAAc,CAAC,IAAiB;gBAC9B,MAAM,MAAM,GAAI,IAA8B,CAAC,MAAM,CAAC;gBACtD,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;oBACtC,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACtE,IAAI,mBAAmB,CAAC,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,EAAE;wBACvD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;wBAEnC,IACE,sCAAsC,CAAC,YAAY,EAAE,QAAQ,CAAC;4BAC9D,CAAC,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC;4BACvC,oBAAoB,CAAC,IAAI,CAAC,EAC1B;4BACA,OAAO,CAAC,MAAM,CAAC;gCACb,OAAO,EAAE,aAAa,CAAC,YAAY,CAAC;gCACpC,IAAI;6BACL,CAAC,CAAC;yBACJ;qBACF;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,aAAa,CAAC,cAAsB;IAC3C,IAAI,kBAAkB,CAAC;IACvB,IAAI,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACpE,kBAAkB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC1E;SAAM;QACL,kBAAkB,GAAG,cAAc,CAAC;KACrC;IACD,OAAO,oBAAoB,kBAAkB,sCAAsC,CAAC;AACtF,CAAC;AAED,SAAS,mBAAmB,CAC1B,gBAAyC,EACzC,QAAgC,EAChC,YAAoB;IAEpB,OAAO,oBAAoB,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,eAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACnG,CAAC;AAED,SAAS,sCAAsC,CAC7C,IAAiB,EACjB,QAAgC;IAEhC,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,YAAY;QAC1B,CAAC,IAAI,CAAC,IAAI,KAAK,kBAAkB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAC9E,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,IAAiB,EAAE,QAAgC;IACxE,MAAM,MAAM,GAAG,2BAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACnD,MAAM,YAAY,GAAG,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC;IACnD,OAAO,CACL,YAAY,KAAK,SAAS;QAC1B,YAAY,CAAC,MAAM,KAAK,CAAC;QACzB,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CACnD,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,YAAyB,EAAE,IAAkB;IACvE,MAAM,MAAM,GAAI,IAAsB,CAAC,MAAM,CAAC;IAC9C,OAAO;IACL,mBAAmB;IACnB,MAAM,KAAK,SAAS;QACpB,MAAM,CAAC,IAAI,KAAK,sBAAsB;QACtC,MAAM,CAAC,QAAQ,KAAK,GAAG;QACvB,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY;QACjC,YAAY,CAAC,IAAI,KAAK,YAAY;QAClC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,CACvC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAiB;IAC7C,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AACtE,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAiB;IAC/C,MAAM,MAAM,GAAI,IAAsB,CAAC,MAAM,CAAC;IAC9C,OAAO,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,qBAAqB,CAAC;AAChD,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAiB;IAC7C,MAAM,SAAS,GAAG,2BAAmB,CAAC,IAAqB,CAAC,CAAC;IAC7D,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,iBAAiB,CAAC,CAAC;IACvF,OAAO,CACL,SAAS,GAAG,CAAC,CAAC;QACd,SAAS;aACN,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;aACnB,KAAK,CAAC,QAAQ,CAAC,EAAE,CAChB,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,CAAC,CAAC,QAAQ,CACxF,QAAQ,CAAC,IAAI,CACd,CACF,CACJ,CAAC;AACJ,CAAC"}