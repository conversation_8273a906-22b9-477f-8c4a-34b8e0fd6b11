{"version": 3, "file": "no-redundant-assignments.js", "sourceRoot": "", "sources": ["../../src/rules/no-redundant-assignments.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,iEAA+E;AAK/E,+DAM+B;AAE/B,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,EAAE,CAC/B,sCAAsC,IAAI,+DAA+D,CAAC;AAC/F,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,aAAa,GAAsB,EAAE,CAAC;QAC5C,MAAM,eAAe,GAAG,IAAI,GAAG,EAA+B,CAAC;QAC/D,2DAA2D;QAC3D,MAAM,cAAc,GAAG,IAAI,GAAG,EAAyB,CAAC;QAExD,OAAO;YACL,0DAA0D,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAChF,qBAAqB,CAAC,IAAsB,CAAC,CAAC;YAChD,CAAC;YACD,+DAA+D,EAAE,GAAG,EAAE;gBACpE,oBAAoB,EAAE,CAAC;YACzB,CAAC;YACD,UAAU,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAChC,IAAI,cAAc,EAAE,EAAE;oBACpB,OAAO;iBACR;gBACD,oBAAoB,CAAC,IAAyB,CAAC,CAAC;YAClD,CAAC;YACD,cAAc,EAAE,GAAG,EAAE;gBACnB,yCAAmB,CAAC,eAAe,CAAC,CAAC;gBACrC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC7B,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;gBACH,eAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,cAAc,CAAC,KAAK,EAAE,CAAC;gBACvB,OAAO,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/B,aAAa,CAAC,GAAG,EAAE,CAAC;iBACrB;YACH,CAAC;YAED,kBAAkB;YAClB,sBAAsB,EAAE,CAAC,OAAwB,EAAE,EAAE;gBACnD,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,yCAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;YACpE,CAAC;YACD,eAAe,EAAE,QAAQ,CAAC,EAAE;gBAC1B,WAAW,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC7C,CAAC;YACD,aAAa,EAAE,GAAG,EAAE;gBAClB,UAAU,EAAE,CAAC;YACf,CAAC;SACF,CAAC;QAEF,SAAS,oBAAoB;YAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,GAAG,EAAG,CAAC;YAC9D,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAoB;YACjD,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,CAAC;QAED,SAAS,YAAY,CAAC,YAAiC;YACrD,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAmB,YAAY,CAAC,EAAE,CAAC,CAAC;YACrE,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACpC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;gBAC9B,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;oBACrD,OAAO;iBACR;gBACD,MAAM,SAAS,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAClD,MAAM,SAAS,GAAG,2CAAqB,CACrC,QAAQ,EACR,GAAG,CAAC,SAAS,EACb,iBAAiB,EACjB,GAAG,CAAC,IAAI,CACT,CAAC;gBACF,IAAI,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,MAAK,gBAAgB,IAAI,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,MAAK,CAAC,EAAE;oBACjE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;oBAChC,yBAAyB,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;iBACjF;gBACD,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,SAAS,yBAAyB,CAChC,EAAE,QAAQ,EAAE,QAAQ,EAAmB,EACvC,IAAwB,EACxB,MAAyB,EACzB,SAAiB,EACjB,IAAY;YAEZ,IAAI,SAAS,CAAC,IAAI,KAAK,cAAc,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;gBAC7D,OAAO;aACR;YACD,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,QAAS,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE;gBACtD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAK;oBACX,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC;iBACvB,CAAC,CAAC;aACJ;QACH,CAAC;QAED,iCAAiC;QACjC,gCAAgC;QAChC,SAAS,iBAAiB,CAAC,QAAwB;YACjD,OAAO,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;QACvE,CAAC;QAED,SAAS,YAAY,CAAC,GAAc;YAClC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YAC9B,OAAO,QAAQ,IAAI,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC,CAAC;QAC5F,CAAC;QAED,SAAS,qBAAqB,CAAC,GAAc;YAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YAE9B,OAAO,CACL,QAAQ;gBACR,CAAC,kBAAkB,CAAC,GAAG,CAAC;gBACxB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;gBAC9B,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC;gBACpC,CAAC,iBAAiB,CAAC,GAAG,CAAC;gBACvB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CACjB,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/E,CACF,CAAC;QACJ,CAAC;QAED,SAAS,cAAc;YACrB,OAAQ,OAAO,CAAC,YAAY,EAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,mBAAmB,CAAC,CAAC;QAC/F,CAAC;QAED,SAAS,6BAA6B,CAAC,QAAwB;YAC7D,OAAO,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAChD,CAAC;QAED,SAAS,oBAAoB,CAAC,IAAuB;YACnD,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACjD,IAAI,GAAG,EAAE;gBACP,gBAAgB,CAAC,GAAG,CAAC,CAAC;aACvB;YACD,IAAI,QAAQ,EAAE;gBACZ,oBAAoB,CAAC,QAAQ,CAAC,CAAC;aAChC;QACH,CAAC;QAED,SAAS,gBAAgB,CAAC,GAAc;YACtC,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC;YAC5D,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;gBACzC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACrB;iBAAM;gBACL,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC7D,MAAM,YAAY,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;oBACrD,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,sBAAsB,CAAC,OAAwB;YACtD,IAAI,IAAI,CAAC;YACT,IAAI,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACnC,IAAI,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAE,CAAC;aACzC;iBAAM;gBACL,IAAI,GAAG,IAAI,yCAAmB,CAAC,OAAO,CAAC,CAAC;gBACxC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;aACvC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,oBAAoB,CAAC,QAAwB;YACpD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,IAAI,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAChC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;aAC/C;iBAAM;gBACL,cAAc,CAAC,GAAG,CAChB,QAAQ,EACR,IAAI,GAAG,CAAS,CAAC,UAAU,CAAC,CAAC,CAC9B,CAAC;aACH;QACH,CAAC;QAED,SAAS,WAAW,CAAC,eAAgC;YACnD,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;QAED,SAAS,UAAU;YACjB,aAAa,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAuB,EACvB,KAAyB;YAEzB,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;aACtC;YACD,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC;YAC9D,IAAI,GAAG,EAAE;gBACP,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;aACxC;iBAAM;gBACL,0EAA0E;gBAC1E,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;gBAClF,IAAI,QAAQ,EAAE;oBACZ,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;iBAChC;gBACD,wGAAwG;gBACxG,qDAAqD;gBACrD,OAAO,2BAA2B,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;aACvD;QACH,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAuB;YAC/C,OAAO,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;CACF,CAAC;AAEF,MAAM,eAAe;IAOnB,YAAY,QAAkB;QAN9B,2BAAsB,GAAG,IAAI,GAAG,EAA+B,CAAC;QAChE,6BAAwB,GAA0B,EAAE,CAAC;QAErD,aAAQ,GAAG,IAAI,GAAG,EAA2B,CAAC;QAC9C,oBAAe,GAAwB,EAAE,CAAC;QAGxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;CACF;AAID,MAAM,iBAAiB;IAGrB,YAAY,IAAoB;QAIhC,QAAG,GAAG,IAAI,GAAG,EAAa,CAAC;QAC3B,QAAG,GAAG,IAAI,GAAG,EAAa,CAAC;QAJzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAKD,KAAK,CAAC,IAAmB;QACvB,OAAO,8BAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IAChG,CAAC;IAED,KAAK,CAAC,IAAmB;QACvB,OAAO,8BAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;IAC7F,CAAC;IAED,GAAG,CAAC,GAAc;QAChB,IAAI,MAAM,GAAG,GAAG,CAAC,UAAuC,CAAC;QACzD,OAAO,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClB,MAAM;aACP;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClB,MAAM;aACP;YACD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SACxB;QACD,IAAI,MAAM,KAAK,IAAI,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACtD;IACH,CAAC;CACF;AAED,SAAS,IAAI,CAAI,GAAa;IAC5B,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAc;;IACvC,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC;IACzB,IAAI,CAAA,MAAA,GAAG,CAAC,SAAS,0CAAE,IAAI,MAAK,YAAY,EAAE;QACxC,MAAM,GAAG,GAAG,+CAAyB,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/D,OAAO,GAAG,KAAK,GAAG,CAAC;KACpB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,oBAAoB,CAAC,SAA6B;IACzD,IAAI,SAAS,IAAI,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;QACnD,MAAM,IAAI,GAAI,SAA2B,CAAC,MAAM,CAAC;QACjD,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAsB,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;KAC9E;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAc;IACxC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE;QACxC,OAAO,KAAK,CAAC;KACd;IACD,MAAM,MAAM,GAAI,GAAG,CAAC,UAAkC,CAAC,MAAM,CAAC;IAC9D,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,mBAAmB,CAAC;AACvD,CAAC"}