{"version": 3, "file": "unverified-certificate.js", "sourceRoot": "", "sources": ["../../src/rules/unverified-certificate.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAKkB;AAEL,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,OAAO,GAAG,kEAAkE,CAAC;QACnF,MAAM,iBAAiB,GAAG,qCAAqC,CAAC;QAChE,SAAS,sBAAsB,CAC7B,cAAqC,EACrC,sBAA8B;YAE9B,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,sBAAsB,GAAG,CAAC,EAAE;gBAChE,OAAO;aACR;YACD,MAAM,iBAAiB,GAAG,cAAc,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;YAC3E,MAAM,kBAAkB,GAAkB,EAAE,CAAC;YAC7C,MAAM,iBAAiB,GAA2B,EAAE,CAAC;YACrD,MAAM,aAAa,GAAG,4BAAoB,CAAC,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;YAC3F,IAAI,CAAC,aAAa,EAAE;gBAClB,OAAO;aACR;YACD,IAAI,iBAAiB,KAAK,aAAa,EAAE;gBACvC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACnC;YACD,MAAM,qCAAqC,GAAG,4BAAoB,CAChE,OAAO,EACP,aAAa,EACb,oBAAoB,EACpB,KAAK,CACN,CAAC;YACF,IAAI,qCAAqC,EAAE;gBACzC,kBAAkB,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBAC/D,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC1C,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,cAAc,CAAC,MAAM;oBAC3B,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;iBAC1E,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,cAAc,GAAG,IAA6B,CAAC;gBACrD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE;oBAC5D,sBAAsB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;iBAC3C;gBACD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE;oBAC1D,sBAAsB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;iBAC3C;gBACD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE;oBAC1D,sBAAsB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;iBAC3C;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}