{"version": 3, "file": "cyclomatic-complexity.js", "sourceRoot": "", "sources": ["../../src/rules/cyclomatic-complexity.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,yEAImD;AACnD,iEAAkE;AAClE,oCAAwE;AAE3D,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,SAAS,EAAE;YACnB;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IAED,MAAM,CAAC,OAAyB;QAC9B,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;QACpC,IAAI,mBAA8D,CAAC;QACnE,IAAI,uBAAsC,CAAC;QAC3C,IAAI,2BAA0C,CAAC;QAC/C,OAAO;YACL,OAAO,EAAE,GAAG,EAAE;gBACZ,mBAAmB,GAAG,IAAI,GAAG,EAA4B,CAAC;gBAC1D,uBAAuB,GAAG,EAAE,CAAC;gBAC7B,2BAA2B,GAAG,EAAE,CAAC;YACnC,CAAC;YACD,cAAc,EAAE,GAAG,EAAE;gBACnB,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;oBAC3C,IACE,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC;wBACvC,CAAC,2BAA2B,CAAC,QAAQ,CAAC,IAAI,CAAC,EAC3C;wBACA,6BAA6B,CAAC,IAAwB,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;qBACrF;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,kEAAkE,EAAE,CAAC,IAAiB,EAAE,EAAE,CACxF,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAS,CAAC,OAAO,CAAC,CAAC;YACnD,mFAAmF,EAAE,CACnF,IAAiB,EACjB,EAAE,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC;YACvC,mGAAmG,EAAE,CACnG,IAAiB,EACjB,EAAE,CAAC,2BAA2B,CAAC,IAAI,CAAE,IAA6B,CAAC,MAAM,CAAC;SAC7E,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,6BAA6B,CACpC,IAAsB,EACtB,MAA+B,EAC/B,SAAiB,EACjB,OAAyB;IAEzB,MAAM,MAAM,GAAG,2BAA2B,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAClE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;IACjC,IAAI,UAAU,GAAG,SAAS,EAAE;QAC1B,OAAO,CAAC,MAAM,CAAC;YACb,OAAO,EAAE,gBAAgB,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC;YACxD,GAAG,EAAE,wCAA4B,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC;SACzD,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,UAAkB,EAClB,SAAiB,EACjB,MAAyB;IAEzB,MAAM,cAAc,GAAmB;QACrC,OAAO,EAAE,gCAAgC,UAAU,0BAA0B,SAAS,cAAc;QACpG,IAAI,EAAE,UAAU,GAAG,SAAS;QAC5B,kBAAkB,EAAE,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC;KACpD,CAAC;IACF,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAsB;IACjD,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;QAC1B,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM;QAC9B,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;QAC3B,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM;QAC/B,OAAO,EAAE,IAAI;KACd,CAAC;AACJ,CAAC;AAED,SAAS,2BAA2B,CAClC,IAAiB,EACjB,MAA+B,EAC/B,OAAyB;IAEzB,MAAM,OAAO,GAAG,IAAI,yBAAyB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACrE,OAAO,CAAC,KAAK,EAAE,CAAC;IAChB,OAAO,OAAO,CAAC,mBAAmB,EAAE,CAAC;AACvC,CAAC;AAMD,MAAM,yBAAyB;IAG7B,YACmB,IAAiB,EACjB,MAA+B,EAC/B,OAAyB;QAFzB,SAAI,GAAJ,IAAI,CAAa;QACjB,WAAM,GAAN,MAAM,CAAyB;QAC/B,YAAO,GAAP,OAAO,CAAkB;QAL3B,WAAM,GAAsB,EAAE,CAAC;IAM7C,CAAC;IAEJ,KAAK;QACH,MAAM,SAAS,GAAG,CAAC,IAAiB,EAAE,EAAE;YACtC,IAAI,KAAyC,CAAC;YAE9C,IAAI,sBAAc,CAAC,IAAI,CAAC,EAAE;gBACxB,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oBACtB,OAAO;iBACR;qBAAM;oBACL,KAAK,GAAG,EAAE,GAAG,EAAE,wCAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;iBAChF;aACF;iBAAM;gBACL,QAAQ,IAAI,CAAC,IAAI,EAAE;oBACjB,KAAK,uBAAuB;wBAC1B,KAAK,GAAG,IAAI,CAAC,OAAO;6BACjB,aAAa,EAAE;6BACf,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;wBAClF,MAAM;oBACR,KAAK,YAAY;wBACf,sBAAsB;wBACtB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;4BACd,MAAM;yBACP;oBACH,KAAK,aAAa,CAAC;oBACnB,KAAK,cAAc,CAAC;oBACpB,KAAK,gBAAgB,CAAC;oBACtB,KAAK,gBAAgB,CAAC;oBACtB,KAAK,gBAAgB,CAAC;oBACtB,KAAK,kBAAkB;wBACrB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;wBACzD,MAAM;oBACR,KAAK,mBAAmB;wBACtB,KAAK,GAAG,IAAI,CAAC,OAAO;6BACjB,aAAa,EAAE;6BACf,aAAa,CACZ,IAAI,CAAC,IAAI,EACT,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAC3E,CAAC;wBACJ,MAAM;iBACT;aACF;YAED,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACzB;YAED,kBAAU,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAChF,CAAC,CAAC;QAEF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF"}