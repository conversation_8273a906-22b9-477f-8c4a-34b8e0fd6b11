{"version": 3, "file": "variable-name.js", "sourceRoot": "", "sources": ["../../src/rules/variable-name.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,gDAAgD;;;AAKhD,oCAA8C;AAOjC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,mBAAmB,EAAE,CAAC,IAAiB,EAAE,EAAE,CACzC,aAAa,CAAC,IAAoC,EAAE,OAAO,CAAC;YAC9D,wKAAwK,EAAE,CACxK,IAAiB,EACjB,EAAE,CAAC,aAAa,CAAC,IAAoB,EAAE,OAAO,CAAC;YACjD,aAAa,EAAE,CAAC,IAAiB,EAAE,EAAE,CACnC,aAAa,CAAE,IAA0C,EAAE,OAAO,CAAC;YACrE,WAAW,EAAE,CAAC,IAAiB,EAAE,EAAE,CAAC,UAAU,CAAC,IAA4B,EAAE,OAAO,CAAC;SACtF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,aAAa,CAAC,IAAkC,EAAE,OAAyB;IAClF,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,OAAO;KACR;IACD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CACtC,0BAAkB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAC9C,wBAAwB,CAAC,EAAE,EAAE,gBAAgB,EAAE,OAAO,CAAC,CACxD,CACF,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,IAAkB,EAAE,OAAyB;IAClE,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,OAAO;KACR;IACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAC1B,0BAAkB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,wBAAwB,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAC5F,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,IAA4B,EAAE,OAAyB;IAC5E,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE;QAClC,wBAAwB,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;KACzD;AACH,CAAC;AAED,SAAS,UAAU,CAAC,MAA4B,EAAE,OAAyB;IACzE,IAAI,MAAM,CAAC,KAAK,EAAE;QAChB,0BAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAC5C,wBAAwB,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CACnD,CAAC;KACH;AACH,CAAC;AAED,SAAS,wBAAwB,CAC/B,EAAuB,EACvB,MAAc,EACd,OAAyB;IAEzB,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;IACrC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;IACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QACvB,OAAO,CAAC,MAAM,CAAC;YACb,OAAO,EAAE,eAAe,MAAM,KAAK,IAAI,qCAAqC,MAAM,GAAG;YACrF,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;KACJ;AACH,CAAC"}