{"version": 3, "file": "no-empty-collection.js", "sourceRoot": "", "sources": ["../../src/rules/no-empty-collection.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,oCAMkB;AAElB,4DAA4D;AAC5D,MAAM,yBAAyB,GAAG;IAChC,gBAAgB;IAChB,YAAY;IACZ,KAAK;IACL,SAAS;IACT,OAAO;IACP,MAAM;IACN,mBAAmB;IACnB,OAAO;IACP,QAAQ;CACT,CAAC;AACF,MAAM,eAAe,GAAG;IACtB,gBAAgB;IAChB,QAAQ;IACR,MAAM;IACN,SAAS;IACT,UAAU;IACV,SAAS;IACT,MAAM;IACN,aAAa;IACb,OAAO;IACP,UAAU;IACV,UAAU;IACV,gBAAgB;IAChB,mBAAmB;IACnB,KAAK;IACL,KAAK;CACN,CAAC;AACF,MAAM,gBAAgB,GAAG;IACvB,SAAS;IACT,OAAO;IACP,QAAQ;IACR,MAAM;IACN,WAAW;IACX,SAAS;IACT,MAAM;IACN,KAAK;IACL,QAAQ;IACR,aAAa;IACb,MAAM;IACN,QAAQ;CACT,CAAC;AAEF,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC;IACrC,GAAG,yBAAyB;IAC5B,GAAG,eAAe;IAClB,GAAG,gBAAgB;CACpB,CAAC,CAAC;AAEU,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,cAAc,EAAE;gBACd,2BAA2B,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;YAC3D,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAgB,2BAA2B,CAAC,KAAkB,EAAE,OAAyB;IACvF,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC3B,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAC1B,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;KACJ;IAED,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QACrC,2BAA2B,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;AACL,CAAC;AAVD,kEAUC;AAED,SAAS,0BAA0B,CAAC,QAAwB,EAAE,OAAyB;IACrF,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE;QACnC,OAAO;KACR;IAED,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,EAAE;QACjF,yDAAyD;QACzD,OAAO;KACR;IAED,MAAM,aAAa,GAAG,EAAE,CAAC;IACzB,IAAI,8BAA8B,GAAG,KAAK,CAAC;IAE3C,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,UAAU,EAAE;QACrC,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;YACrB,IAAI,mCAAmC,CAAC,GAAG,CAAC,EAAE;gBAC5C,8BAA8B,GAAG,IAAI,CAAC;aACvC;iBAAM;gBACL,4EAA4E;gBAC5E,oEAAoE;gBACpE,OAAO;aACR;SACF;aAAM,IAAI,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACxC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACzB;aAAM;YACL,4CAA4C;YAC5C,2EAA2E;YAC3E,OAAO;SACR;KACF;IAED,IAAI,8BAA8B,EAAE;QAClC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC1B,OAAO,CAAC,MAAM,CAAC;gBACb,OAAO,EAAE,yBAAyB,GAAG,CAAC,UAAU,CAAC,IAAI,iCAAiC;gBACtF,IAAI,EAAE,GAAG,CAAC,UAAU;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,mCAAmC,CAAC,GAAoB;IAC/D,MAAM,cAAc,GAAG,iCAAyB,CAC9C,GAAG,CAAC,UAA2B,EAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAqB,CAC1D,CAAC;IACjB,IAAI,cAAc,EAAE;QAClB,IAAI,cAAc,CAAC,IAAI,KAAK,oBAAoB,IAAI,cAAc,CAAC,IAAI,EAAE;YACvE,OAAO,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SACnD;QAED,IAAI,cAAc,CAAC,IAAI,KAAK,qBAAqB,EAAE;YACjD,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;YAC7C,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,sBAAsB;gBAC1C,qBAAa,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;gBACnC,qBAAqB,CAAC,UAAU,CAAC,KAAK,CAAC,CACxC,CAAC;SACH;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAAC,IAAiB;IAC9C,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,EAAE;QAC3C,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;KACnC;SAAM,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAgB,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,EAAE;QACpF,OAAO,oBAAY,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,6BAAqB,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC;KAC3F;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,wBAAwB,CAAC,GAAoB;IACpD,OAAO,2BAA2B,CAAC,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC;AAC9F,CAAC;AAED,SAAS,2BAA2B,CAAC,KAAsB;IACzD,MAAM,MAAM,GAAI,KAAK,CAAC,UAA4B,CAAC,MAAM,CAAC;IAC1D,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;QAChD,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7C,IAAI,sBAAsB,IAAI,sBAAsB,CAAC,IAAI,KAAK,gBAAgB,EAAE;YAC9E,OAAO,oBAAY,CAAC,MAAM,CAAC,QAAuB,EAAE,GAAG,sBAAsB,CAAC,CAAC;SAChF;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAAC,GAAoB;IACjD,MAAM,kBAAkB,GAAG,iCAAyB,CAClD,GAAG,CAAC,UAA2B,EAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAgB,CACX,CAAC;IAEvD,OAAO,kBAAkB,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC;AAC3E,CAAC;AAED,SAAS,aAAa,CAAC,GAAoB;IACzC,MAAM,MAAM,GAAI,GAAG,CAAC,UAA4B,CAAC,MAAM,CAAC;IACxD,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACpG,CAAC;AAED,SAAS,cAAc,CAAC,gBAA2C;IACjE,MAAM,SAAS,GAAG,sBAAc,CAAC,gBAAgB,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;IAC9D,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAsB,CACN,CAAC;IACnC,IAAI,UAAU,IAAI,UAAU,CAAC,QAAQ,KAAK,GAAG,EAAE;QAC7C,OAAO,CAAC,gBAAgB,EAAE,GAAG,SAAS,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KACnE;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}