{"version": 3, "file": "no-weak-keys.js", "sourceRoot": "", "sources": ["../../src/rules/no-weak-keys.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAA2F;AAE3F,MAAM,sBAAsB,GAAG,IAAI,CAAC;AACpC,MAAM,sBAAsB,GAAG,GAAG,CAAC;AACnC,MAAM,WAAW,GAAG;IAClB,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,YAAY;CACb,CAAC;AAEW,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,SAAS,eAAe,CAAC,IAA6B;YACpD,MAAM,OAAO,GAAG,4BAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAC/D,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE;gBAChD,OAAO,OAAO,CAAC,KAAK,CAAC;aACtB;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,qBAAqB,CAAC,SAAiB,EAAE,OAAoB;YACpE,MAAM,eAAe,GAAG,mCAA2B,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YAC9E,MAAM,aAAa,GAAG,eAAe,CAAC,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,eAAe,IAAI,aAAa,IAAI,aAAa,GAAG,sBAAsB,EAAE;gBAC9E,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,oCAAoC,sBAAsB,aAAa,SAAS,oBAAoB;iBAC9G,CAAC,CAAC;aACJ;YACD,MAAM,eAAe,GAAG,mCAA2B,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YAC9E,MAAM,aAAa,GAAG,eAAe,CAAC,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,eAAe,IAAI,aAAa,IAAI,aAAa,GAAG,sBAAsB,EAAE;gBAC9E,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,oCAAoC,sBAAsB,aAAa,SAAS,oBAAoB;iBAC9G,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,YAAY,CAAC,OAAoB;;YACxC,MAAM,kBAAkB,GAAG,mCAA2B,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC9E,MAAM,UAAU,GAAG,MAAA,MAAA,4BAAoB,CACrC,OAAO,EACP,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,KAAK,EACzB,SAAS,CACV,0CAAE,KAAK,0CAAE,QAAQ,EAAE,CAAC;YACrB,IAAI,kBAAkB,IAAI,UAAU,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBACxE,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE,GAAG,UAAU,yDAAyD;iBAChF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;;gBACpC,MAAM,cAAc,GAAG,IAA6B,CAAC;gBACrD,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;gBAClC,IACE,MAAM,CAAC,IAAI,KAAK,kBAAkB;oBAClC,oBAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,EAAE,qBAAqB,CAAC,EACvE;oBACA,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;wBACvC,OAAO;qBACR;oBACD,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC,GAAG,cAAc,CAAC,SAAS,CAAC;oBACzD,MAAM,UAAU,GAAG,4BAAoB,CAAC,OAAO,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;oBAC9E,IAAI,CAAC,UAAU,EAAE;wBACf,OAAO;qBACR;oBACD,MAAM,SAAS,GAAG,MAAA,4BAAoB,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,0CAAE,KAAK,CAAC;oBAChF,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE;wBAC9C,qBAAqB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;qBAC9C;yBAAM,IAAI,SAAS,KAAK,IAAI,EAAE;wBAC7B,YAAY,CAAC,UAAU,CAAC,CAAC;qBAC1B;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}