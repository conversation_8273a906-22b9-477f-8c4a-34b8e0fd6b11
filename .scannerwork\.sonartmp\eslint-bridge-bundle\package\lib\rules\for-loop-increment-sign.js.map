{"version": 3, "file": "for-loop-increment-sign.js", "sourceRoot": "", "sources": ["../../src/rules/for-loop-increment-sign.ts"], "names": [], "mappings": ";;;AAqBA,yEAA6E;AAEhE,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,YAAY,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAClC,MAAM,YAAY,GAAwB,IAA2B,CAAC;gBACtE,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;gBAC/B,MAAM,aAAa,GAA4B,gBAAgB,CAAC,gBAAgB,CAC9E,YAAY,CACb,CAAC;gBACF,IAAI,IAAI,IAAI,IAAI,IAAI,aAAa,IAAI,IAAI,IAAI,YAAY,CAAC,MAAM,IAAI,IAAI,EAAE;oBACxE,OAAO;iBACR;gBACD,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;gBAC9D,IAAI,cAAc,KAAK,CAAC,IAAI,cAAc,KAAK,aAAa,CAAC,SAAS,EAAE;oBACtE,MAAM,QAAQ,GAAW,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC;oBAC5E,MAAM,OAAO,GAAG,4BAAgB,CAC9B,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,QAAQ,QAAQ,2CAA2C,EAC5F,CAAC,IAAI,CAAC,CACP,CAAC;oBACF,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO;wBACP,IAAI,EAAE,YAAY,CAAC,MAAM;qBAC1B,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,MAAM,gBAAgB;IAKpB,YAAY,SAA4B,EAAE,UAA6B,EAAE,SAAiB;QACxF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,YAAiC;QACvD,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QACD,IAAI,UAAU,CAAC,IAAI,KAAK,kBAAkB,EAAE;YAC1C,MAAM,gBAAgB,GAA4B,UAAU,CAAC;YAC7D,MAAM,SAAS,GAAW,gBAAgB,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;SAC7F;QACD,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAsB,EAAE;YAC9C,MAAM,oBAAoB,GAAgC,UAAU,CAAC;YACrE,IACE,oBAAoB,CAAC,QAAQ,KAAK,IAAI;gBACtC,oBAAoB,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAC/C;gBACA,MAAM,GAAG,gBAAgB,CAAC,SAAS,CACjC,UAAU,EACV,oBAAoB,CAAC,IAAI,EACzB,kBAAkB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAC/C,CAAC;aACH;YACD,IACE,oBAAoB,CAAC,QAAQ,KAAK,IAAI;gBACtC,oBAAoB,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAC/C;gBACA,MAAM,GAAG,gBAAgB,CAAC,SAAS,CACjC,UAAU,EACV,oBAAoB,CAAC,IAAI,EACzB,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAChD,CAAC;aACH;YACD,IAAI,oBAAoB,CAAC,QAAQ,KAAK,GAAG,EAAE;gBACzC,MAAM,GAAG,gBAAgB,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;aACrE;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,SAAS,CACtB,SAA4B,EAC5B,UAA6B,EAC7B,SAAiB;QAEjB,IAAI,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE;YACpC,OAAO,IAAI,gBAAgB,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;SAC/D;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,oBAAiD;QAClF,MAAM,GAAG,GAAG,oBAAoB,CAAC,IAAI,CAAC;QACtC,MAAM,GAAG,GAAG,oBAAoB,CAAC,KAAK,CAAC;QACvC,IACE,GAAG,CAAC,IAAI,KAAK,YAAY;YACzB,GAAG,CAAC,IAAI,KAAK,kBAAkB;YAC/B,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,EAC9C;YACA,IAAI,kBAAkB,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACvD,IAAI,kBAAkB,KAAK,IAAI,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;gBAClE,kBAAkB,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,kBAAkB,CAAC;gBACrF,OAAO,gBAAgB,CAAC,SAAS,CAAC,oBAAoB,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;aAClF;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,SAAS,kBAAkB,CAAC,UAA6B;IACvD,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE;QACjC,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;YAC/B,OAAO,CAAC,CAAC;SACV;QACD,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB,EAAE;QACzC,MAAM,eAAe,GAA2B,UAAU,CAAC;QAC3D,IAAI,eAAe,CAAC,QAAQ,KAAK,GAAG,EAAE;YACpC,OAAO,kBAAkB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;SACrD;QACD,IAAI,eAAe,CAAC,QAAQ,KAAK,GAAG,EAAE;YACpC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;SACtD;KACF;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,iBAAiB,CACxB,SAA4B,EAC5B,gBAAkC;IAElC,IAAI,SAAS,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACzC,OAAO,CAAC,CAAC;KACV;IACD,IAAI,gBAAgB,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,CAAC,UAAU,CAAC,EAAE;QACjE,IAAI,SAAS,CAAC,QAAQ,KAAK,GAAG,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC7D,OAAO,CAAC,CAAC,CAAC;SACX;QACD,IAAI,SAAS,CAAC,QAAQ,KAAK,GAAG,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC7D,OAAO,CAAC,CAAC,CAAC;SACX;KACF;SAAM,IAAI,gBAAgB,CAAC,SAAS,CAAC,KAAK,EAAE,gBAAgB,CAAC,UAAU,CAAC,EAAE;QACzE,IAAI,SAAS,CAAC,QAAQ,KAAK,GAAG,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC7D,OAAO,CAAC,CAAC,CAAC;SACX;QACD,IAAI,SAAS,CAAC,QAAQ,KAAK,GAAG,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC7D,OAAO,CAAC,CAAC,CAAC;SACX;KACF;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,gBAAgB,CAAC,UAA6B,EAAE,UAA6B;IACpF,OAAO,UAAU,CAAC,IAAI,KAAK,YAAY,IAAI,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC;AACjF,CAAC"}