{"version": 3, "file": "no-unenclosed-multiline-block.js", "sourceRoot": "", "sources": ["../../src/rules/no-unenclosed-multiline-block.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAMjD,MAAM,oBAAoB,GAAG;IAC3B,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;CACjB,CAAC;AAWW,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,OAAO,EAAE,CAAC,IAAiB,EAAE,EAAE,CAAC,eAAe,CAAE,IAAuB,CAAC,IAAI,EAAE,OAAO,CAAC;YACvF,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE,CACpC,eAAe,CAAE,IAA8B,CAAC,IAAI,EAAE,OAAO,CAAC;YAChE,aAAa,EAAE,CAAC,IAAiB,EAAE,EAAE,CACnC,eAAe,CAAG,IAA2C,CAAC,IAAmB,EAAE,OAAO,CAAC;SAC9F,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,eAAe,CAAC,UAAuB,EAAE,OAAyB;IACzE,KAAK,CAAC,UAAU,CAAC;SACd,MAAM,CAAC,iBAAiB,CAAC,EAAE,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC;SAC9D,OAAO,CAAC,sBAAsB,CAAC,EAAE;QAChC,IAAI,sBAAsB,CAAC,WAAW,EAAE,EAAE;YACxC,mBAAmB,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;SACtD;aAAM,IAAI,sBAAsB,CAAC,eAAe,EAAE,EAAE;YACnD,eAAe,CACb,sBAAsB,EACtB,4BAA4B,CAAC,sBAAsB,CAAC,IAAI,EAAE,UAAU,CAAC,EACrE,OAAO,CACR,CAAC;SACH;aAAM,IAAI,sBAAsB,CAAC,qBAAqB,EAAE,EAAE;YACzD,2BAA2B,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;SAC9D;IACH,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,KAAK,CAAC,UAAuB;IACpC,OAAO,UAAU;SACd,MAAM,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE;QACtC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;YACzD,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;SACtD;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,IAAI,KAAK,EAA+C,CAAC;SAC3D,GAAG,CAAC,IAAI,CAAC,EAAE;QACV,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,eAAe,CAAC,SAA2B;IAClD,IAAI,SAAS,CAAC,IAAI,KAAK,aAAa,EAAE;QACpC,IAAI,SAAS,CAAC,SAAS,EAAE;YACvB,OAAO,SAAS,CAAC,SAAS,CAAC;SAC5B;aAAM;YACL,OAAO,SAAS,CAAC,UAAU,CAAC;SAC7B;KACF;SAAM;QACL,OAAO,SAAS,CAAC,IAAI,CAAC;KACvB;AACH,CAAC;AAED,SAAS,4BAA4B,CAAC,SAAoB,EAAE,UAAuB;IACjF,MAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;IAC9C,IAAI,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC;IACtC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;QAClC,MAAM,eAAe,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC;QAC7C,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC;QACxD,IAAI,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE;YAClC,IAAI,kBAAkB,KAAK,WAAW,CAAC,MAAM,EAAE;gBAC7C,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC;aAC3C;iBAAM;gBACL,MAAM;aACP;SACF;KACF;IACD,OAAO,cAAc,GAAG,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,mBAAmB,CAAC,kBAAqC,EAAE,OAAyB;IAC3F,OAAO,CAAC,MAAM,CAAC;QACb,OAAO,EACL,uCAAuC,kBAAkB,CAAC,0BAA0B,EAAE,sCAAsC;YAC5H,yBAAyB,kBAAkB,CAAC,2BAA2B,EAAE,GAAG;QAC9E,IAAI,EAAE,kBAAkB,CAAC,IAAI;KAC9B,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe,CACtB,eAAkC,EAClC,UAAkB,EAClB,OAAyB;IAEzB,OAAO,CAAC,MAAM,CAAC;QACb,OAAO,EACL,kCAAkC,eAAe,CAAC,0BAA0B,EAAE,iCAAiC,UAAU,uBAAuB;YAChJ,yBAAyB,eAAe,CAAC,2BAA2B,EAAE,GAAG;QAC3E,IAAI,EAAE,eAAe,CAAC,IAAI;KAC3B,CAAC,CAAC;AACL,CAAC;AAED,SAAS,2BAA2B,CAClC,iBAAoC,EACpC,OAAyB;IAEzB,OAAO,CAAC,MAAM,CAAC;QACb,OAAO,EACL,kCAAkC,iBAAiB,CAAC,0BAA0B,EAAE,sCAAsC;YACtH,yBAAyB,iBAAiB,CAAC,2BAA2B,EAAE,GAAG;QAC7E,IAAI,EAAE,iBAAiB,CAAC,IAAI;KAC7B,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAiB;IAC3C,OAAO,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,iBAAiB;IAGrB,YACW,YAA8B,EAC9B,IAAe,EACf,IAAe;QAFf,iBAAY,GAAZ,YAAY,CAAkB;QAC9B,SAAI,GAAJ,IAAI,CAAW;QACf,SAAI,GAAJ,IAAI,CAAW;QAExB,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG;YACf,YAAY,EAAE,WAAW,CAAC,KAAK;YAC/B,UAAU,EAAE,WAAW,CAAC,GAAG;YAC3B,SAAS,EAAE,YAAY,CAAC,KAAK;YAC7B,OAAO,EAAE,YAAY,CAAC,GAAG;YACzB,SAAS,EAAE,YAAY,CAAC,KAAK;YAC7B,OAAO,EAAE,YAAY,CAAC,GAAG;SAC1B,CAAC;IACJ,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAgB,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;IACvE,CAAC;IAEM,eAAe;QACpB,OAAO,CACL,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAC7F,CAAC;IACJ,CAAC;IAEM,qBAAqB;QAC1B,OAAO,CACL,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI;YAChE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CACrE,CAAC;IACJ,CAAC;IAEM,0BAA0B;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC;IAClF,CAAC;IAEM,2BAA2B;QAChC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW,CAAC;IACpF,CAAC;IAEO,cAAc;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC;IAC9E,CAAC;CACF;AAWD,SAAS,QAAQ,CAAC,IAAiB;IACjC,OAAQ,IAAsB,CAAC,GAAG,CAAC;AACrC,CAAC"}