{"version": 3, "file": "todo-tag.js", "sourceRoot": "", "sources": ["../../src/rules/todo-tag.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,MAAM,WAAW,GAAG,sDAAsD,CAAC;AAC3E,MAAM,WAAW,GAAG,MAAM,CAAC;AAEd,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,cAAc,EAAE,GAAG,EAAE;gBACnB,sBAAsB,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;YAC5D,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAgB,sBAAsB,CACpC,OAAyB,EACzB,OAAe,EACf,OAAe;IAEf,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAC1C,UAAU,CAAC,cAAc,EAAyB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QACpE,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAE5C,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC7B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE;oBAC3D,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO;wBACP,GAAG,EAAE,kBAAkB,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;qBACpD,CAAC,CAAC;iBACJ;aACF;SACF;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAvBD,wDAuBC;AAED,SAAS,cAAc,CAAC,IAAY,EAAE,KAAa,EAAE,OAAe;IAClE,MAAM,GAAG,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;IAEnC,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IACjE,MAAM,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAExE,OAAO,GAAG,IAAI,IAAI,CAAC;AACrB,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAe,EACf,KAAa,EACb,OAAyB,EACzB,OAAe;IAEf,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;IAC9C,MAAM,WAAW,GAAG,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,MAAM,YAAY,GAAG,WAAW,GAAG,KAAK,CAAC;IAEzC,OAAO;QACL,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE;QACrC,GAAG,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,MAAM,EAAE;KACrD,CAAC;AACJ,CAAC"}