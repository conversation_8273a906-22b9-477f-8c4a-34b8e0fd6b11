{"version": 3, "file": "new-operator-misuse.js", "sourceRoot": "", "sources": ["../../src/rules/new-operator-misuse.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAIjD,+CAAiC;AACjC,oCAKkB;AACL,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,QAAQ,EAAE;YAClB;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QACD,OAAO;YACL,8CAA8C,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpE,MAAM,EAAE,MAAM,EAAE,GAAG,IAA4B,CAAC;gBAChD,MAAM,IAAI,GAAG,2BAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM,SAAS,GAAG,8BAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACzD,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACnE,MAAM,aAAa,GAAG,OAAO;yBAC1B,aAAa,EAAE;yBACf,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC;oBACxF,MAAM,QAAQ,GAAG,OAAO;yBACrB,aAAa,EAAE;yBACf,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAE,CAAC;oBACpF,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAC1F,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAC,CAAC,aAAc,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAI,CAAC;oBACpF,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,wBAAgB,CAAC,WAAW,IAAI,+BAA+B,EAAE,CAAC,QAAQ,CAAC,CAAC;wBACrF,GAAG;qBACJ,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,cAAc,CACrB,IAAa,EACb,SAAmC,EACnC,aAAsB;IAEtB,OAAO,CACL,OAAO,CAAC,IAAI,CAAC;QACb,QAAQ,CAAC,IAAI,CAAC;QACd,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC;QAC7C,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,IAAa;IAC5B,OAAO,CACL,IAAI,CAAC,MAAM;QACX,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC;YAC/C,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CACnD,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,IAAa;IAC7B,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1E,CAAC;AAED,SAAS,UAAU,CAAC,IAAa;IAC/B,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC5E,CAAC;AAED,SAAS,aAAa,CAAC,IAAa,EAAE,SAAmC,EAAE,aAAsB;IAC/F,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,aAAa,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAmC;IAC7D,OAAO,CACL,SAAS,KAAK,SAAS;QACvB,SAAS,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAClF,CAAC;AACJ,CAAC;AAED,SAAS,KAAK,CAAC,IAAa;IAC1B,OAAO,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;AACzC,CAAC"}