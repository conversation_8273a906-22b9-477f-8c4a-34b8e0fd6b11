{"version": 3, "file": "sockets.js", "sourceRoot": "", "sources": ["../../src/rules/sockets.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAAwF;AAExF,MAAM,UAAU,GAAG,KAAK,CAAC;AAEzB,MAAM,OAAO,GAAG,8CAA8C,CAAC;AAE/D,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC,CAAC;AAE3E,MAAM,kBAAkB,GAAG,QAAQ,CAAC;AAEvB,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,aAAa,EAAE,CAAC,IAAiB,EAAE,EAAE,CACnC,mBAAmB,CAAC,IAA4B,EAAE,OAAO,CAAC;YAC5D,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE,CACpC,mBAAmB,CAAC,IAA6B,EAAE,OAAO,CAAC;SAC9D,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,mBAAmB,CAAC,EAAE,MAAM,EAAE,IAAI,EAAyB,EAAE,OAAyB;IAC7F,IAAI,UAAU,CAAC;IACf,IAAI,UAAyC,CAAC;IAC9C,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE;QAC7E,UAAU,GAAG,iCAAyB,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/D,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;KAC9B;IAED,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE;QAChC,UAAU,GAAG,yCAAiC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAChE,UAAU,GAAG,MAAM,CAAC;KACrB;IAED,IAAI,UAAU,IAAI,cAAc,CAAC,UAAU,EAAE,IAAI,KAAK,eAAe,EAAE,UAAU,CAAC,EAAE;QAClF,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;KACpD;AACH,CAAC;AAED,SAAS,cAAc,CACrB,UAA6B,EAC7B,aAAsB,EACtB,UAA2B;IAE3B,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,KAAK,KAAK,UAAU,IAAI,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE;QACtF,OAAO,KAAK,CAAC;KACd;IAED,IAAI,aAAa,EAAE;QACjB,OAAO,UAAU,CAAC,IAAI,KAAK,kBAAkB,CAAC;KAC/C;IAED,OAAO,yBAAyB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACxD,CAAC"}