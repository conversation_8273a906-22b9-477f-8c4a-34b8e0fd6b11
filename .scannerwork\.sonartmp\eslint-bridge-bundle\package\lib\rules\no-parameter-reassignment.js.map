{"version": 3, "file": "no-parameter-reassignment.js", "sourceRoot": "", "sources": ["../../src/rules/no-parameter-reassignment.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,iEAAkE;AAElE,oCAA8D;AAajD,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,IAAI,oBAAoB,GAAwB;YAC9C,IAAI,EAAE,QAAQ;YACd,8BAA8B,EAAE,IAAI,GAAG,EAAU;YACjD,gBAAgB,EAAE,IAAI,GAAG,EAAU;YACnC,aAAa,EAAE,IAAI,GAAG,EAAU;YAChC,sBAAsB,EAAE,IAAI,GAAG,EAAsC;SACtE,CAAC;QAEF,SAAS,oBAAoB,CAC3B,UAA6B,EAC7B,qBAAkC;YAElC,IAAI,oBAAoB,CAAC,IAAI,KAAK,qBAAqB,EAAE;gBACvD,OAAO;aACR;YAED,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC;YACrC,MAAM,gBAAgB,GAAG,YAAY,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;YACxE,IACE,gBAAgB;gBAChB,CAAC,gBAAgB,CAAC,IAAI;gBACtB,CAAC,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,EACrD;gBACA,IACE,oBAAoB,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC;oBACvD,gBAAgB,CAAC,WAAW,EAAE;oBAC9B,CAAC,uBAAuB,CAAC,YAAY,EAAE,gBAAgB,CAAC,SAAS,CAAC,EAClE;oBACA,2GAA2G;oBAC3G,6BAA6B;oBAC7B,IACE,2BAA2B,CAAC,OAAO,CAAC;wBACpC,OAAO,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC,aAAa;sBAC7E;wBACA,OAAO;qBACR;oBACD,UAAU,CAAC,gBAAgB,CAAC,CAAC;iBAC9B;gBACD,UAAU,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;aAChD;iBAAM,IAAI,YAAY,KAAK,WAAW,EAAE;gBACvC,8BAA8B,CAAC,oBAAoB,CAAC,CAAC;aACtD;QACH,CAAC;QAED,SAAS,uBAAuB,CAAC,YAAoB,EAAE,SAA6B;YAClF,OAAO,CACL,SAAS;gBACT,OAAO;qBACJ,aAAa,EAAE;qBACf,aAAa,CACZ,SAAS,EACT,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,KAAK,WAAW,CACrE,CACJ,CAAC;QACJ,CAAC;QAED,SAAS,UAAU,CAAC,SAA0B;YAC5C,MAAM,cAAc,GAAG,wBAAwB,CAAC,SAAS,CAAC,CAAC;YAC3D,OAAO,CAAC,MAAM,CAAC;gBACb,OAAO,EACL,oDAAoD;oBACpD,uBAAuB,SAAS,CAAC,UAAU,CAAC,IAAI,IAAI;gBACtD,GAAG,cAAc;aAClB,CAAC,CAAC;QACL,CAAC;QAED,SAAS,UAAU;YACjB,oBAAoB,GAAG,oBAAoB,CAAC,aAAa;gBACvD,CAAC,CAAC,oBAAoB,CAAC,aAAa;gBACpC,CAAC,CAAC,oBAAoB,CAAC;QAC3B,CAAC;QAED,OAAO;YACL,eAAe,CAAC,SAAwB,EAAE,IAAiB;gBACzD,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACxC,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,KAAK,UAAU,EAAE;oBACpD,MAAM,EACJ,sBAAsB,EACtB,gBAAgB,EAChB,8BAA8B,GAC/B,GAAG,qBAAqB,CAAC,oBAAoB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBAE/D,MAAM,YAAY,GAAG,eAAe,CAAC,IAAiC,CAAC,CAAC;oBACxE,IAAI,YAAY,EAAE;wBAChB,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;qBACvC;oBAED,oBAAoB,GAAG;wBACrB,IAAI,EAAE,UAAU;wBAChB,aAAa,EAAE,oBAAoB;wBACnC,gBAAgB;wBAChB,sBAAsB;wBACtB,8BAA8B;wBAC9B,aAAa,EAAE,oBAAoB,CACjC,oBAAoB,CAAC,aAAa,EAClC,8BAA8B,CAC/B;qBACF,CAAC;iBACH;qBAAM;oBACL,oBAAoB,GAAG;wBACrB,IAAI,EAAE,QAAQ;wBACd,aAAa,EAAE,oBAAoB;wBACnC,8BAA8B,EAAE,IAAI,GAAG,EAAU;wBACjD,gBAAgB,EAAE,IAAI,GAAG,EAAU;wBACnC,aAAa,EAAE,IAAI,GAAG,EAAU;wBAChC,sBAAsB,EAAE,IAAI,GAAG,EAAsC;qBACtE,CAAC;iBACH;YACH,CAAC;YAED,qBAAqB,CACnB,YAAkC,EAClC,UAAgC,EAChC,IAAiB;gBAEjB,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;gBAClC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;oBACrC,OAAO;iBACR;gBACD,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC/E,MAAM,EACJ,sBAAsB,EACtB,gBAAgB,EAChB,8BAA8B,GAC/B,GAAG,qBAAqB,CAAC,oBAAoB,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;gBAEtE,IAAI,YAAY,EAAE;oBAChB,KAAK,MAAM,GAAG,IAAI,YAAY,CAAC,UAAU,EAAE;wBACzC,sBAAsB,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;qBACjD;iBACF;gBAED,sHAAsH;gBACtH,0BAAkB,CAAC,MAAM,CAAC,IAAqB,EAAE,IAAI,CAAC;qBACnD,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;qBAClC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACd,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC3B,8BAA8B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC;gBAEL,oBAAoB,GAAG;oBACrB,IAAI,EAAE,SAAS;oBACf,aAAa,EAAE,oBAAoB;oBACnC,8BAA8B;oBAC9B,gBAAgB;oBAChB,aAAa,EAAE,oBAAoB,CACjC,oBAAoB,CAAC,aAAa,EAClC,8BAA8B,CAC/B;oBACD,sBAAsB;iBACvB,CAAC;YACJ,CAAC;YAED,sBAAsB,CAAC,QAA8B,EAAE,IAAiB;gBACtE,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE;oBAC/B,OAAO;iBACR;gBAED,MAAM,EACJ,sBAAsB,EACtB,gBAAgB,EAChB,8BAA8B,GAC/B,GAAG,qBAAqB,CAAC,oBAAoB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;gBAE/D,oBAAoB,GAAG;oBACrB,IAAI,EAAE,OAAO;oBACb,aAAa,EAAE,oBAAoB;oBACnC,8BAA8B;oBAC9B,gBAAgB;oBAChB,aAAa,EAAE,oBAAoB,CACjC,oBAAoB,CAAC,aAAa,EAClC,8BAA8B,CAC/B;oBACD,sBAAsB;iBACvB,CAAC;YACJ,CAAC;YAED,aAAa,EAAE,UAAU;YACzB,qBAAqB,EAAE,UAAU;YACjC,qBAAqB,EAAE,UAAU;YACjC,kBAAkB,EAAE,UAAU;YAC9B,wCAAwC,EAAE,CAAC,IAAiB,EAAE,EAAE,CAC9D,oBAAoB,CAAC,IAAyB,EAAE,UAAU,CAAC;YAC7D,yCAAyC,EAAE,CAAC,IAAiB,EAAE,EAAE,CAC/D,oBAAoB,CAAC,IAAyB,EAAE,SAAS,CAAC;YAC5D,yCAAyC,EAAE,CAAC,IAAiB,EAAE,EAAE,CAC/D,oBAAoB,CAAC,IAAyB,EAAE,SAAS,CAAC;YAC5D,yCAAyC,EAAE,CAAC,IAAiB,EAAE,EAAE,CAC/D,oBAAoB,CAAC,IAAyB,EAAE,OAAO,CAAC;SAC3D,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,2BAA2B,CAAC,OAAyB;IAC5D,MAAM,iBAAiB,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,aAAa,CAE5E,CAAC;IACd,IAAI,iBAAiB,EAAE;QACrB,OAAO,CACL,eAAe,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,gBAAgB,CAAC,CAAC;YAC7D,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,sBAAc,CAAC,CACjE,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAiC,EACjC,YAAsB;IAEtB,OAAO,CAAC,CAAC,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxD,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,CAAc,EAAE,CAAc;IAC1D,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,eAAe,CAAC,IAA+B;IACtD,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;AACxC,CAAC;AAED,SAAS,kBAAkB,CACzB,IAAiB,EACjB,MAAoB;IAEpB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,gBAAgB;QAC9B,CAAC,CAAC,MAAM;QACR,CAAC,MAAM,CAAC,IAAI,KAAK,gBAAgB,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAC5B,oBAAyC,EACzC,OAAyB,EACzB,IAAiB;IAEjB,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAsC,CAAC;IAC7E,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAS,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;IAChF,MAAM,8BAA8B,GAAG,IAAI,GAAG,EAAU,CAAC;IACzD,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACpD,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,8BAA8B,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClD,KAAK,MAAM,UAAU,IAAI,QAAQ,CAAC,UAAU,EAAE;YAC5C,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;SAC/D;IACH,CAAC,CAAC,CAAC;IACH,OAAO,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,8BAA8B,EAAE,CAAC;AACtF,CAAC;AAED,SAAS,UAAU,CAAC,OAA4B,EAAE,YAAoB;IACpE,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACxC,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE;QACtF,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;KACjD;AACH,CAAC;AAED,SAAS,8BAA8B,CAAC,oBAAyC;IAC/E,IAAI,eAAe,GAAoC,oBAAoB,CAAC;IAC5E,OAAO,eAAe,IAAI,eAAe,CAAC,IAAI,KAAK,UAAU,EAAE;QAC7D,eAAe,GAAG,eAAe,CAAC,aAAa,CAAC;KACjD;IAED,IAAI,eAAe,EAAE;QACnB,KAAK,MAAM,YAAY,IAAI,eAAe,CAAC,8BAA8B,EAAE;YACzE,eAAe,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;SACjD;KACF;AACH,CAAC;AAED,SAAS,wBAAwB,CAC/B,SAA0B;IAE1B,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/C,IAAI,aAAa,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;QACnE,OAAO,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC;KAClF;IACD,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC;AACxC,CAAC;AAED,SAAS,YAAY,CACnB,oBAAyC,EACzC,UAA6B;IAE7B,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACxF,IAAI,CAAC,mBAAmB,IAAI,oBAAoB,CAAC,aAAa,EAAE;QAC9D,OAAO,YAAY,CAAC,oBAAoB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KACrE;IACD,OAAO,mBAAmB,CAAC;AAC7B,CAAC"}