{"version": 3, "file": "no-undefined-argument.js", "sourceRoot": "", "sources": ["../../src/rules/no-undefined-argument.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAIjD,oCAA4E;AAE5E,+CAAiC;AAEpB,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACtC,OAAO;gBACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;oBACpC,MAAM,IAAI,GAAG,IAA6B,CAAC;oBAC3C,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;oBACjC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,OAAO;qBACR;oBAED,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC3C,IAAI,WAAW,CAAC,YAAY,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE;wBACrF,OAAO,CAAC,MAAM,CAAC;4BACb,OAAO,EAAE,oCAAoC;4BAC7C,IAAI,EAAE,YAAY;yBACnB,CAAC,CAAC;qBACJ;gBACH,CAAC;aACF,CAAC;SACH;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAC;AAEF,SAAS,WAAW,CAAC,IAAiB;IACpC,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC;AACjE,CAAC;AAED,SAAS,mBAAmB,CAC1B,UAAkB,EAClB,IAA2B,EAC3B,QAAgC;IAEhC,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO;SAC/B,cAAc,EAAE;SAChB,oBAAoB,CACnB,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAqB,CAA0B,CACnF,CAAC;IACJ,IAAI,SAAS,EAAE;QACb,MAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;QAC1C,IAAI,WAAW,IAAI,yBAAyB,CAAC,WAAW,CAAC,EAAE;YACzD,MAAM,EAAE,UAAU,EAAE,GAAG,WAAW,CAAC;YACnC,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;YACzC,OAAO,SAAS,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,aAAa,CAAC,CAAC;SACxE;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,yBAAyB,CAChC,WAA2B;IAE3B,OAAO;QACL,EAAE,CAAC,UAAU,CAAC,mBAAmB;QACjC,EAAE,CAAC,UAAU,CAAC,kBAAkB;QAChC,EAAE,CAAC,UAAU,CAAC,aAAa;QAC3B,EAAE,CAAC,UAAU,CAAC,iBAAiB;QAC/B,EAAE,CAAC,UAAU,CAAC,WAAW;QACzB,EAAE,CAAC,UAAU,CAAC,WAAW;QACzB,EAAE,CAAC,UAAU,CAAC,WAAW;KAC1B,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC"}