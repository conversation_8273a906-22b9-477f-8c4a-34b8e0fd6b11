{"version": 3, "file": "for-in.js", "sourceRoot": "", "sources": ["../../src/rules/for-in.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKpC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,SAAS,UAAU,CAAC,SAAsB;YACxC,IAAI,SAAS,CAAC,IAAI,KAAK,qBAAqB,EAAE;gBAC5C,OAAO,KAAK,CAAC;aACd;YACD,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;YACxC,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,sBAAsB;gBAC1C,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,kBAAkB;gBAC3C,UAAU,CAAC,IAAI,CAAC,QAAQ,CACzB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,cAAc,CAAC,IAAI;gBACjB,MAAM,cAAc,GAAG,IAA6B,CAAC;gBACrD,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;gBAEjC,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE;oBAClC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC1B,OAAO;qBACR;oBACD,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACpC,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,IAAI,UAAU,CAAC,cAAc,CAAC,EAAE;wBACvE,OAAO;qBACR;iBACF;gBAED,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;oBACrF,OAAO;iBACR;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,2DAA2D;iBACrE,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}