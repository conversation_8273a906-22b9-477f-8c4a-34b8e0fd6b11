{"version": 3, "file": "x-powered-by.js", "sourceRoot": "", "sources": ["../../src/rules/x-powered-by.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAAmE;AACnE,mDAA0C;AAE1C,MAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,MAAM,eAAe,GAAG,iBAAiB,CAAC;AAC1C,MAAM,mBAAmB,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;AACzD,MAAM,OAAO,GAAG,8EAA8E,CAAC;AAC/F,MAAM,sBAAsB,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;AACzD,iDAAiD;AACjD,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAEd,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,IAAI,gBAAgB,GAA6B,IAAI,CAAC;QACtD,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,OAAO;YACL,OAAO;gBACL,gBAAgB,GAAG,IAAI,CAAC;gBACxB,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YACD,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,IAAI,CAAC,MAAM,IAAI,gBAAgB,EAAE;oBAC/B,MAAM,QAAQ,GAAG,IAA6B,CAAC;oBAC/C,MAAM;wBACJ,uBAAO,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;4BACrF,oBAAoB,CAAC,QAAQ,EAAE,gBAAgB,CAAC;4BAChD,oBAAoB,CAAC,QAAQ,EAAE,gBAAgB,CAAC;4BAChD,aAAa,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;iBAC7C;YACH,CAAC;YACD,kBAAkB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACxC,IAAI,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE;oBAChC,MAAM,OAAO,GAAG,IAAiC,CAAC;oBAClD,MAAM,GAAG,GAAG,uBAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBAClE,IAAI,GAAG,EAAE;wBACP,gBAAgB,GAAG,GAAG,CAAC;qBACxB;iBACF;YACH,CAAC;YACD,eAAe,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACrC,IAAI,CAAC,MAAM,IAAI,gBAAgB,EAAE;oBAC/B,MAAM,GAAG,GAAG,IAA8B,CAAC;oBAC3C,MAAM,GAAG,0BAA0B,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;iBAC5D;YACH,CAAC;YACD,cAAc;gBACZ,IAAI,CAAC,MAAM,IAAI,gBAAgB,EAAE;oBAC/B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,gBAAgB;wBACtB,OAAO,EAAE,OAAO;qBACjB,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF;;GAEG;AACH,SAAS,yBAAyB,CAAC,OAAyB,EAAE,CAAc;;IAC1E,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAgB,EAAE;QAC/B,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;QACxB,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,kBAAkB;YAClC,CAAA,MAAA,2BAAmB,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,0CAAE,KAAK,MAAK,MAAM;YAC7D,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;YACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CACzC,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,YAAY,CAAC,OAAyB;IAC7C,OAAO,CAAC,CAAc,EAAE,EAAE,CACxB,uBAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;QAChE,yBAAyB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,oBAAoB,CAC3B,cAAqC,EACrC,GAAsB;IAEtB,IAAI,0BAAkB,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE;QAC9D,MAAM,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,KAAK,mBAAmB,CAAC;KAC5F;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,oBAAoB,CAC3B,cAAqC,EACrC,GAAsB;IAEtB,IAAI,0BAAkB,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,gBAAgB,CAAC,EAAE;QACzE,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,cAAc,CAAC,SAAS,CAAC;QACrD,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,SAAS;YAC7B,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,KAAK,mBAAmB;YAC9D,KAAK,CAAC,IAAI,KAAK,SAAS;YACxB,KAAK,CAAC,KAAK,KAAK,KAAK,CACtB,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,aAAa,CAAC,QAA+B,EAAE,GAAsB;IAC5E,OAAO,OAAO,CACZ,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CACnF,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B,CAAC,GAA2B,EAAE,GAAsB;IACrF,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC;IACzB,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5E,CAAC"}