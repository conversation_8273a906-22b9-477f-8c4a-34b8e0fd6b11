{"version": 3, "file": "weak-ssl.js", "sourceRoot": "", "sources": ["../../src/rules/weak-ssl.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAKkB;AAElB,MAAM,8BAA8B,GAAG;IACrC,gBAAgB;IAChB,uBAAuB;IACvB,uBAAuB;IACvB,YAAY;IACZ,mBAAmB;IACnB,mBAAmB;CACpB,CAAC;AAEW,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,SAAS,kBAAkB,CACzB,gBAAqD,EACrD,YAAoB;YAEpB,MAAM,cAAc,GAAG,mCAA2B,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;YACnF,IAAI,cAAc,EAAE;gBAClB,OAAO,4BAAoB,CAAC,OAAO,EAAE,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;aACvE;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,kBAAkB,CAAC,YAAoB,EAAE,QAAoC;YACpF,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,SAAS,IAAI,QAAQ,CAAC,KAAK,KAAK,OAAO,CAAC,EAAE;gBAC5E,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,YAAY,YAAY,8BAA8B;iBAChE,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,eAAe,CAAC,WAAoC;;YAC3D,MAAM,OAAO,GAAG,4BAAoB,CAAC,OAAO,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAC/E,MAAM,UAAU,GAAG,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC7D,MAAM,UAAU,GAAG,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC7D,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAC7C,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAE7C,MAAM,cAAc,GAAG,kBAAkB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;YACrE,MAAM,mBAAmB,GAAG,MAAA,MAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,KAAK,0CAAE,QAAQ,EAAE,mCAAI,EAAE,CAAC;YACpE,IAAI,cAAc,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBACnF,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,kDAAkD;iBAC5D,CAAC,CAAC;aACJ;YAED,MAAM,aAAa,GAAG,mCAA2B,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YAC5E,IAAI,aAAa,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;gBAC/D,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE,2DAA2D;iBACrE,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,oBAAoB,CAAC,OAAoB;YAChD,MAAM,KAAK,GAAa,EAAE,CAAC;YAC3B,4BAA4B,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,CACL,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI;gBACjB,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAC3E,CAAC;QACJ,CAAC;QAED,SAAS,4BAA4B,CAAC,IAAiB,EAAE,GAAsB;;YAC7E,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE;gBACpC,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC7C,4BAA4B,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;aAC/C;iBAAM,IACL,IAAI,CAAC,IAAI,KAAK,kBAAkB;gBAChC,CAAA,MAAA,2BAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,0CAAE,KAAK,MAAK,WAAW;gBAChE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY,EACnC;gBACA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aAC9B;iBAAM;gBACL,wFAAwF;gBACxF,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;aACf;QACH,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,cAAc,GAAG,IAA6B,CAAC;gBACrD,qEAAqE;gBACrE,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE;oBAC5D,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC9C;gBACD,qDAAqD;gBACrD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE;oBAC1D,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC9C;gBACD,mEAAmE;gBACnE,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE;oBAC1D,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC9C;gBACD,sEAAsE;gBACtE,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,qBAAqB,CAAC,EAAE;oBACtE,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC9C;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}