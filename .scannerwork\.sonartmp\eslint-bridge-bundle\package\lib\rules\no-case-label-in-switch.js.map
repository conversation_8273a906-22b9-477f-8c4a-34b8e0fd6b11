{"version": 3, "file": "no-case-label-in-switch.js", "sourceRoot": "", "sources": ["../../src/rules/no-case-label-in-switch.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKpC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,KAAK,GAAa,CAAC,CAAC,CAAC,CAAC;QAC5B,SAAS,SAAS;YAChB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,GAAG,CAAC,CAAC,CAAC;QAC/B,CAAC;QACD,SAAS,SAAS;YAChB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,GAAG,CAAC,CAAC,CAAC;QAC/B,CAAC;QACD,SAAS,MAAM;YACb,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC;QACD,OAAO;YACL,UAAU,EAAE,GAAG,EAAE;gBACf,SAAS,EAAE,CAAC;YACd,CAAC;YACD,gBAAgB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACtC,IAAI,MAAM,EAAE,EAAE;oBACZ,MAAM,KAAK,GAAI,IAAgC,CAAC,KAAK,CAAC;oBACtD,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,2BAA2B,KAAK,CAAC,IAAI,UAAU;wBACxD,IAAI,EAAE,KAAK;qBACZ,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,yCAAyC,EAAE,GAAG,EAAE;gBAC9C,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC;YACD,iBAAiB,EAAE,GAAG,EAAE;gBACtB,SAAS,EAAE,CAAC;YACd,CAAC;YACD,+CAA+C,EAAE,GAAG,EAAE;gBACpD,KAAK,CAAC,GAAG,EAAE,CAAC;YACd,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}