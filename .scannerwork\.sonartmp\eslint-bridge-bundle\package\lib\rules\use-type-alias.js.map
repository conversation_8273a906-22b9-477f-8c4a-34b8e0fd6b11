{"version": 3, "file": "use-type-alias.js", "sourceRoot": "", "sources": ["../../src/rules/use-type-alias.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,oCAA4C;AAE5C,MAAM,cAAc,GAAG,CAAC,CAAC;AACzB,MAAM,eAAe,GAAG,CAAC,CAAC;AAEb,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,IAAI,KAAmC,CAAC;QACxC,OAAO;YACL,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,EAA2B,CAAC;YAC3D,cAAc,EAAE,GAAG,EAAE,CACnB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACpB,IAAI,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;oBAClC,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;oBAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC;oBACpE,MAAM,OAAO,GAAG,wBAAgB,CAC9B,gBAAgB,IAAI,0BAA0B,EAC9C,IAAI,EACJ,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CACjD,CAAC;oBACF,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;iBAC5C;YACH,CAAC,CAAC;YACJ,iCAAiC,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACvD,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzC,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAChC,QAAQ,CAAC,EAAE,CAAE,QAA0B,CAAC,IAAI,KAAK,wBAAwB,CAC1E,CAAC;gBACF,IAAI,CAAC,WAAW,EAAE;oBAChB,MAAM,SAAS,GAAI,IAAsE,CAAC;oBAC1F,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,EAAE;wBAC3C,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK;6BACzB,GAAG,CAAC,QAAQ,CAAC,EAAE,CACd,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAE,QAAmC,CAAC,CACtE;6BACA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;6BAClC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACb,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAI,CAAC,WAAW,EAAE;4BAChB,WAAW,GAAG,CAAC,SAAS,CAAC,CAAC;4BAC1B,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;yBAC9B;6BAAM;4BACL,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC7B;qBACF;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}