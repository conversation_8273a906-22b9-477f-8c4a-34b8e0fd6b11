{"version": 3, "file": "misplaced-loop-counter.js", "sourceRoot": "", "sources": ["../../src/rules/misplaced-loop-counter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,6EAA4E;AAC5E,iEAAkE;AAElE,MAAM,OAAO;IAIX,YAAqB,OAA4B;QAA5B,YAAO,GAAP,OAAO,CAAqB;QAHjD,uBAAkB,GAAkB,EAAE,CAAC;QACvC,sBAAiB,GAAkB,EAAE,CAAC;IAEc,CAAC;CACtD;AAEY,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,YAAY,GAAc,EAAE,CAAC;QAEnC,SAAS,IAAI,CAAC,WAA0B;YACtC,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnF,CAAC;QAED,SAAS,cAAc,CAAC,IAAiB;YACvC,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;QAED,SAAS,YAAY,CAAC,IAAiB;YACrC,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;QAED,SAAS,QAAQ,CACf,IAAiB,EACjB,QAA6E;YAE7E,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3B,MAAM,WAAW,GAAG,OAAO,EAAE,CAAC;gBAC9B,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC3C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvB,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACnD,IAAI,YAAY,EAAE;oBAChB,OAAO,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,YAAY,KAAK,eAAe,CAAC,CAAC;iBAC9E;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,OAAO;YACd,OAAO,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO;YACL,YAAY,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAClC,YAAY,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAA2B,CAAC,CAAC,CAAC;YAC9D,CAAC;YACD,mBAAmB,EAAE,GAAG,EAAE;gBACxB,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,EAAG,CAAC;gBACpC,IAAI,OAAO,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;oBACpE,OAAO;iBACR;gBACD,MAAM,eAAe,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAClE,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAC5C,2BAAa,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,CAChE,CACF,CAAC;gBAEF,IAAI,CAAC,eAAe,EAAE;oBACpB,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAE,CAAC,GAAG;wBAChE,OAAO,EAAE,qCAAqC,IAAI,CAChD,OAAO,CAAC,iBAAiB,CAC1B,kCAAkC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI;qBACxE,CAAC,CAAC;iBACJ;YACH,CAAC;YAED,mCAAmC,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACzD,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;oBACxB,MAAM,IAAI,GAAI,IAAoC,CAAC,IAAI,CAAC;oBACxD,MAAM,mBAAmB,GAAkB,EAAE,CAAC;oBAC9C,0BAA0B,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;oBACtD,MAAM,EAAE,kBAAkB,EAAE,GAAG,OAAO,EAAE,CAAC;oBACzC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;iBAClE;YACH,CAAC;YAED,+BAA+B,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACrD,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;oBACxB,OAAO,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAE,IAAgC,CAAC,QAAQ,CAAC,CAAC;iBAC/E;YACH,CAAC;YAED,6BAA6B,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBACzB,OAAO;iBACR;gBACD,MAAM,MAAM,GAAG,eAAe,CAAC,IAA6B,CAAC,CAAC;gBAC9D,IAAI,MAAM,EAAE;oBACV,OAAO,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC3C;YACH,CAAC;YAED,yBAAyB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC/C,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;oBACtB,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAE,CAAC;oBACnC,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;wBACnF,OAAO,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACxC;iBACF;YACH,CAAC;YAED,+BAA+B,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACrD,IACE,YAAY,CAAC,IAAI,CAAC;oBAClB,iBAAS,CAAC,OAAO,CAAE,CAAC,IAAI,KAAK,kBAAkB;oBAC/C,iBAAS,CAAC,OAAO,CAAE,CAAC,IAAI,KAAK,gBAAgB,EAC7C;oBACA,OAAO,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACxC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,eAAe,CAAC,IAA2B;IAClD,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IACzB,OAAO,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACzC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;KACxB;IACD,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;QAC1D,OAAO,MAAM,CAAC;KACf;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAwB,EAAE,QAAmC;IAC/F,QAAQ,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,EAAE;QAClB,KAAK,cAAc;YACjB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,0BAA0B,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;YAChF,MAAM;QACR,KAAK,eAAe;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,0BAA0B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;YACpF,MAAM;QACR,KAAK,UAAU;YACb,0BAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACjD,MAAM;QACR,KAAK,mBAAmB;YACtB,0BAA0B,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAChD,MAAM;QACR;YACE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACvB;AACH,CAAC"}