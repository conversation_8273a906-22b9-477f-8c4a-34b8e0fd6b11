{"version": 3, "file": "file-header.js", "sourceRoot": "", "sources": ["../../src/rules/file-header.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,MAAM,OAAO,GAAG,wCAAwC,CAAC;AAEzD,IAAI,MAMH,CAAC;AAEW,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE7B,IAAI,MAAM,CAAC,eAAe,EAAE;YAC1B,uBAAuB;YACvB,OAAO,EAAE,CAAC;SACX;QAED,OAAO;YACL,cAAc,EAAE;gBACd,IAAI,MAAM,CAAC,mBAAmB,EAAE;oBAC9B,sBAAsB,CAAC,MAAM,CAAC,aAAc,EAAE,OAAO,CAAC,CAAC;iBACxD;qBAAM;oBACL,cAAc,CAAC,MAAM,CAAC,aAAc,EAAE,OAAO,CAAC,CAAC;iBAChD;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,cAAc,CAAC,aAAuB,EAAE,OAAyB;IACxE,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC;IAE5C,IAAI,aAAa,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;QACxC,OAAO,GAAG,IAAI,CAAC;QAEf,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,EAAE,CAAC;YACJ,IAAI,IAAI,KAAK,YAAY,EAAE;gBACzB,OAAO,GAAG,KAAK,CAAC;gBAChB,MAAM;aACP;SACF;KACF;IAED,IAAI,CAAC,OAAO,EAAE;QACZ,YAAY,CAAC,OAAO,CAAC,CAAC;KACvB;AACH,CAAC;AAED,SAAS,sBAAsB,CAAC,aAAqB,EAAE,OAAyB;IAC9E,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,CAAC;IACtD,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE;QAC/B,YAAY,CAAC,OAAO,CAAC,CAAC;KACvB;AACH,CAAC;AAED,SAAS,YAAY,CAAC,OAAyB;IAC7C,OAAO,CAAC,MAAM,CAAC;QACb,OAAO;QACP,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;KAC5B,CAAC,CAAC;AACL,CAAC;AAED,SAAS,WAAW,CAAC,OAAc;IACjC,MAAM,CAAC,EAAE,YAAY,EAAE,mBAAmB,EAAE,CAAC,GAAG,OAAO,CAAC;IAExD,IACE,CAAC,MAAM;QACP,MAAM,CAAC,YAAY,KAAK,YAAY;QACpC,MAAM,CAAC,mBAAmB,KAAK,mBAAmB,EAClD;QACA,MAAM,GAAG;YACP,YAAY;YACZ,mBAAmB;SACpB,CAAC;QAEF,IAAI,mBAAmB,EAAE;YACvB,IAAI;gBACF,MAAM,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;gBACrD,MAAM,CAAC,eAAe,GAAG,KAAK,CAAC;aAChC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;gBACpF,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;aAC/B;SACF;aAAM;YACL,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;SAC3D;KACF;AACH,CAAC"}