{"version": 3, "file": "no-invariant-returns.js", "sourceRoot": "", "sources": ["../../src/rules/no-invariant-returns.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,iEAAkE;AAClE,yEAAyF;AACzF,oCAKkB;AAeL,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IAED,MAAM,CAAC,OAAyB;QAC9B,MAAM,oBAAoB,GAAsB,EAAE,CAAC;QAEnD,MAAM,mBAAmB,GAAG,CAAC,IAAiB,EAAE,EAAE,CAChD,8BAA8B,CAAC,IAAI,EAAE,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAE9F,SAAS,8BAA8B,CAAC,IAAiB,EAAE,eAAiC;YAC1F,IAAI,CAAC,eAAe,IAAI,uBAAuB,CAAC,eAAe,CAAC,EAAE;gBAChE,OAAO;aACR;YAED,MAAM,cAAc,GAAG,eAAe,CAAC,gBAAgB,CAAC,GAAG,CACzD,eAAe,CAAC,EAAE,CAAC,eAAe,CAAC,QAAuB,CAC3D,CAAC;YACF,IAAI,eAAe,CAAC,cAAc,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACvD,MAAM,OAAO,GAAG,wBAAgB,CAC9B,6DAA6D,EAC7D,cAAiC,EACjC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAC1C,cAAc,CAAC,MAAM,CACtB,CAAC;gBAEF,OAAO,CAAC,MAAM,CAAC;oBACb,OAAO;oBACP,GAAG,EAAE,wCAA4B,CAAC,IAAuB,EAAE,iBAAS,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;iBACxF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,eAAe,CAAC,QAAuB;gBACrC,oBAAoB,CAAC,IAAI,CAAC;oBACxB,QAAQ;oBACR,0BAA0B,EAAE,KAAK;oBACjC,gBAAgB,EAAE,EAAE;iBACrB,CAAC,CAAC;YACL,CAAC;YACD,aAAa;gBACX,oBAAoB,CAAC,GAAG,EAAE,CAAC;YAC7B,CAAC;YACD,eAAe,CAAC,IAAiB;gBAC/B,MAAM,cAAc,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7E,IAAI,cAAc,EAAE;oBAClB,MAAM,eAAe,GAAG,IAA8B,CAAC;oBACvD,cAAc,CAAC,0BAA0B;wBACvC,cAAc,CAAC,0BAA0B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;oBACzE,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;iBACvD;YACH,CAAC;YACD,0BAA0B,EAAE,mBAAmB;YAC/C,yBAAyB,EAAE,mBAAmB;YAC9C,8BAA8B,EAAE,mBAAmB;SACpD,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,uBAAuB,CAAC,eAAgC;IAC/D,mFAAmF;IACnF,sFAAsF;IACtF,uDAAuD;IACvD,MAAM,iBAAiB,GAAG,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CACrE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAC7B,CAAC;IAEF,OAAO,CACL,iBAAiB;QACjB,eAAe,CAAC,0BAA0B;QAC1C,eAAe,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC;QAC5C,eAAe,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CACnD,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,cAA6B,EAAE,KAAkB;IACxE,MAAM,kBAAkB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAM,UAAU,GAAG,eAAe,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IAC9D,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,OAAO,cAAc;aAClB,KAAK,CAAC,CAAC,CAAC;aACR,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,CAAC,KAAK,UAAU,CAAC,CAAC;KACjF;SAAM,IAAI,kBAAkB,CAAC,IAAI,KAAK,YAAY,EAAE;QACnD,MAAM,mBAAmB,GAAG,wBAAwB,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACrF,IAAI,mBAAmB,EAAE;YACvB,MAAM,wBAAwB,GAAG,mBAAmB,CAAC,QAAQ,CAAC,UAAU;iBACrE,KAAK,CAAC,CAAC,CAAC;iBACR,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC9B,OAAO,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAC1C,wBAAwB,CAAC,QAAQ,CAAC,aAAkC,CAAC,CACtE,CAAC;SACH;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,wBAAwB,CAC/B,YAAoB,EACpB,KAAkB;IAElB,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC7C,IAAI,QAAQ,EAAE;QACZ,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,sBAAsB,CAAC,GAAG,CAAC,CAAC,EAAE;YACzE,IAAI,cAAc,GAAG,IAAI,CAAC;YAC1B,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE;gBACtE,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;aAC7C;YACD,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC;SACrC;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAoB;IAClD,MAAM,mBAAmB,GAAG,iCAAyB,CACnD,GAAG,CAAC,UAA2B,EAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,sBAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9C,CAAC;IAE7B,oGAAoG;IACpG,kCAAkC;IAClC,OAAO,CACL,mBAAmB;QACnB,mBAAmB,CAAC,IAAI,KAAK,qBAAqB;QAClD,CAAC,sBAAc,CAAC,mBAAmB,EAAE,GAAG,CAAC;YACvC,mBAAmB,CAAC,UAAU,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAC5D,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,aAA0B,EAAE,KAAkB;IACrE,IAAI,aAAa,CAAC,IAAI,KAAK,SAAS,EAAE;QACpC,OAAO,aAAa,CAAC,KAAK,CAAC;KAC5B;SAAM,IAAI,aAAa,CAAC,IAAI,KAAK,iBAAiB,EAAE;QACnD,MAAM,kBAAkB,GAAG,eAAe,CAAC,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1E,OAAO,kBAAkB,KAAK,SAAS;YACrC,CAAC,CAAC,8BAA8B,CAAC,aAAa,CAAC,QAAQ,EAAE,kBAAkB,CAAC;YAC5E,CAAC,CAAC,SAAS,CAAC;KACf;SAAM,IAAI,aAAa,CAAC,IAAI,KAAK,YAAY,EAAE;QAC9C,MAAM,mBAAmB,GAAG,wBAAwB,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAChF,IAAI,mBAAmB,IAAI,mBAAmB,CAAC,cAAc,EAAE;YAC7D,OAAO,eAAe,CAAC,mBAAmB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;SACnE;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,8BAA8B,CAAC,QAAgB,EAAE,kBAAgC;IACxF,QAAQ,QAAQ,EAAE;QAChB,KAAK,GAAG;YACN,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACrC,KAAK,GAAG;YACN,OAAO,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACpC,KAAK,GAAG;YACN,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACrC,KAAK,GAAG;YACN,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACtC,KAAK,QAAQ;YACX,OAAO,OAAO,kBAAkB,CAAC;QACnC;YACE,OAAO,SAAS,CAAC;KACpB;AACH,CAAC"}