{"version": 3, "file": "sonar-no-unused-vars.js", "sourceRoot": "", "sources": ["../../src/rules/sonar-no-unused-vars.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKpC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,IAAI,QAAQ,GAAwB,EAAE,CAAC;QACvC,IAAI,qBAAqB,GAAa,EAAE,CAAC;QAEzC,SAAS,aAAa,CAAC,CAAiB,EAAE,OAAqC;YAC7E,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvB,OAAO;aACR;YACD,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC5B,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,cAAc,EAAE;gBAClD,OAAO;aACR;YACD,IAAI,OAAO,KAAK,oBAAoB,EAAE;gBACpC,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,qBAAqB,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;oBACxF,OAAO;iBACR;aACF;YAED,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;YAExE,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACpF,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,KAAK,cAAc,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CACjB,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,GAAG;oBACT,OAAO;iBACR,CAAC,CACH,CAAC;aACH;QACH,CAAC;QAED,SAAS,qBAAqB,CAAC,KAAkB;YAC/C,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS,UAAU,CACjB,KAAkB,EAClB,eAAyD;YAEzD,IAAI,OAAO,GAAG,eAAe,CAAC;YAC9B,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,EAAE;gBAC9D,OAAO,GAAG,KAAK,CAAC;aACjB;iBAAM,IAAI,eAAe,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBAClE,OAAO,GAAG,oBAAoB,CAAC;aAChC;YAED,IAAI,OAAO,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,0BAA0B,EAAE;gBACtE,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,OAAuC,CAAC,CAAC,CAAC;aACzF;YAED,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO;YACL,aAAa,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACnC,MAAM,QAAQ,GAAI,IAA6B,CAAC,UAAU,CAAC;gBAC3D,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAE,OAAe,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC;gBAElF,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO;iBACR;gBAED,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBACzB,IACE,OAAO,CAAC,IAAI,KAAK,UAAU;wBAC3B,OAAO,CAAC,SAAS;wBACjB,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,EACnC;wBACA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;qBAC9B;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,aAAa,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACnC,sEAAsE;gBACtE,qBAAqB,CAAC,IAAI,CAAE,IAAY,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC;YAED,cAAc,EAAE,GAAG,EAAE;gBACnB,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAC;gBAC1C,QAAQ,GAAG,EAAE,CAAC;gBACd,qBAAqB,GAAG,EAAE,CAAC;YAC7B,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,UAAU,CAAC,IAAY,EAAE,UAAmB;IACnD,IAAI,UAAU,EAAE;QACd,OAAO,2BAA2B,IAAI,IAAI,CAAC;KAC5C;SAAM;QACL,OAAO,yCAAyC,IAAI,aAAa,CAAC;KACnE;AACH,CAAC"}