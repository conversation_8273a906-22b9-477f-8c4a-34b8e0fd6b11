{"version": 3, "file": "no-clear-text-protocols.js", "sourceRoot": "", "sources": ["../../src/rules/no-clear-text-protocols.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAGjD,iEAAkE;AAElE,6BAA0B;AAC1B,oCAKkB;AAElB,MAAM,kBAAkB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC9D,MAAM,gBAAgB,GAAG,8DAA8D,CAAC;AACxF,MAAM,oBAAoB,GAAG;IAC3B,YAAY;IACZ,gBAAgB;IAChB,qBAAqB;IACrB,4BAA4B;IAC5B,UAAU;IACV,UAAU;IACV,WAAW;IACX,oBAAoB;IACpB,QAAQ;IACR,cAAc;IACd,eAAe;IACf,aAAa;IACb,0BAA0B;IAC1B,iBAAiB;CAClB,CAAC;AACF,MAAM,mBAAmB,GAAG,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;AAErF,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,SAAS,eAAe,CAAC,cAAqC;YAC5D,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1F,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO;aACR;YACD,MAAM,aAAa,GAAG,4BAAoB,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAClF,MAAM,MAAM,GAAG,mCAA2B,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YACpE,MAAM,UAAU,GAAG,mCAA2B,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;YAC5E,MAAM,IAAI,GAAG,mCAA2B,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAChE,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,CAAC,EAAE;gBAC/E,OAAO;aACR;YACD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,CAAC,EAAE;gBAC3F,OAAO;aACR;YACD,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,EAAE;gBACvE,OAAO;aACR;YACD,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,SAAS,cAAc,CAAC,cAAqC;;YAC3D,IACE,cAAc,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB;gBACjD,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;gBACpD,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EACjD;gBACA,MAAM,aAAa,GAAG,4BAAoB,CACxC,OAAO,EACP,cAAc,CAAC,MAAM,CAAC,MAAM,EAC5B,eAAe,CAChB,CAAC;gBACF,IACE,CAAC,CAAC,aAAa;oBACf,CAAA,MAAA,2BAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,MAAM,CAAC,0CAAE,KAAK,MAAK,KAAK,EACnE;oBACA,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC1F,IAAI,CAAC,QAAQ,EAAE;wBACb,OAAO;qBACR;oBACD,MAAM,aAAa,GAAG,4BAAoB,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC;oBAClF,MAAM,MAAM,GAAG,mCAA2B,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;oBACpE,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;wBAC7E,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,cAAc,CAAC,MAAM;4BAC3B,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC;yBAC3B,CAAC,CAAC;qBACJ;iBACF;aACF;QACH,CAAC;QAED,SAAS,kBAAkB,CAAC,cAAqC;YAC/D,IAAI,cAAc,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY,IAAI,cAAc,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC3F,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC1F,IACE,QAAQ;oBACR,QAAQ,CAAC,IAAI,KAAK,SAAS;oBAC3B,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;oBAClC,QAAQ,CAAC,KAAK,KAAK,eAAe,EAClC;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,UAAU,CAAC,QAAQ,CAAC;qBAC9B,CAAC,CAAC;iBACJ;aACF;QACH,CAAC;QAED,SAAS,cAAc,CAAC,KAAa;YACnC,IAAI,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACtC,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;gBAClC,OAAO,CAAC,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,kBAAkB,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC;aAC1E;YACD,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;QAED,SAAS,gBAAgB,CAAC,KAAa;YACrC,IAAI,GAAG,CAAC;YAER,IAAI;gBACF,GAAG,GAAG,IAAI,SAAG,CAAC,KAAK,CAAC,CAAC;aACtB;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,KAAK,CAAC;aACd;YAED,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;YAC1B,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;gBACjB,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC3B,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,KAAK,IAAI,CAAC;gBAC1D,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC7B,MAAM,OAAO,GAAG,IAAsB,CAAC;gBACvC,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE;oBACrC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,CAAC;oBACvD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACjF,IAAI,QAAQ,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;wBACtC,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;wBAC9D,OAAO,CAAC,MAAM,CAAC;4BACb,OAAO,EAAE,UAAU,CAAC,QAAQ,CAAC;4BAC7B,IAAI;yBACL,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;YACD,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,cAAc,GAAG,IAA6B,CAAC;gBACrD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,CAAC,EAAE;oBACzE,eAAe,CAAC,cAAc,CAAC,CAAC;iBACjC;gBACD,cAAc,CAAC,cAAc,CAAC,CAAC;gBAC/B,kBAAkB,CAAC,cAAc,CAAC,CAAC;YACrC,CAAC;YACD,iBAAiB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACvC,MAAM,iBAAiB,GAAG,IAAgC,CAAC;gBAC3D,IACE,OAAO,iBAAiB,CAAC,MAAM,CAAC,KAAK,KAAK,QAAQ;oBAClD,iBAAiB,CAAC,MAAM,CAAC,KAAK,KAAK,eAAe,EAClD;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,iBAAiB,CAAC,MAAM;wBAC9B,OAAO,EAAE,UAAU,CAAC,QAAQ,CAAC;qBAC9B,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,UAAU,CAAC,QAAgB;IAClC,IAAI,WAAW,CAAC;IAChB,QAAQ,QAAQ,EAAE;QAChB,KAAK,MAAM;YACT,WAAW,GAAG,OAAO,CAAC;YACtB,MAAM;QACR,KAAK,KAAK;YACR,WAAW,GAAG,mBAAmB,CAAC;YAClC,MAAM;QACR;YACE,WAAW,GAAG,KAAK,CAAC;KACvB;IACD,OAAO,SAAS,QAAQ,8BAA8B,WAAW,WAAW,CAAC;AAC/E,CAAC"}