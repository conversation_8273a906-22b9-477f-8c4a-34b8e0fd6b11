{"version": 3, "file": "standard-input.js", "sourceRoot": "", "sources": ["../../src/rules/standard-input.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAA8C;AAEjC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,gBAAgB,CAAC,IAAiB;gBAChC,IAAI,0BAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE;oBAChD,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,yDAAyD;wBAClE,IAAI;qBACL,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}