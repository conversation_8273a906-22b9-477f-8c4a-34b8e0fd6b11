{"version": 3, "file": "post-message.js", "sourceRoot": "", "sources": ["../../src/rules/post-message.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,oCAMkB;AAElB,MAAM,OAAO,GAAG,iEAAiE,CAAC;AAClF,MAAM,YAAY,GAAG,aAAa,CAAC;AACnC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC;AAEjC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QACD,OAAO;YACL,CAAC,wCAAwC,YAAY,8BAA8B,YAAY,KAAK,CAAC,EAAE,CACrG,IAAiB,EACjB,EAAE;gBACF,oBAAoB,CAAC,IAA6B,EAAE,OAAO,CAAC,CAAC;YAC/D,CAAC;YACD,CAAC,wCAAwC,kBAAkB,IAAI,CAAC,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACtF,yBAAyB,CAAC,IAA6B,EAAE,OAAO,CAAC,CAAC;YACpE,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,cAAc,CAAC,IAAiB,EAAE,OAAyB;IAClE,MAAM,IAAI,GAAG,uBAAe,CAAC,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IAC3D,MAAM,aAAa,GAAG,iBAAiB,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1E,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC;AAC7E,CAAC;AAED,SAAS,oBAAoB,CAAC,QAA+B,EAAE,OAAyB;;IACtF,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IAC5B,iDAAiD;IACjD,IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC;QAC3C,CAAA,MAAA,4BAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,0CAAE,KAAK,MAAK,GAAG,EAC9E;QACA,OAAO;KACR;IACD,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE;QAChC,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;KACJ;IACD,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACtC,OAAO;KACR;IACD,IAAI,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE;QAC1C,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,yBAAyB,CAAC,QAA+B,EAAE,OAAyB;IAC3F,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC;IAC7C,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACvD,OAAO;KACR;IACD,MAAM,QAAQ,GAAG,uBAAe,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAC7C,OAAO;KACR;IACD,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACjC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;QAC/B,OAAO;KACR;IACD,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,wBAAwB,CACrE,QAAQ,CAAC,IAAI,EACb,KAAK,EACL,OAAO,CACR,CAAC;IACF,IAAI,CAAC,iBAAiB,EAAE;QACtB,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;KACJ;AACH,CAAC;AAED,MAAM,iBAAiB;IAAvB;QACU,kBAAa,GAAG,KAAK,CAAC;IAiBhC,CAAC;IAfC,MAAM,CAAC,kBAAkB,CAAC,IAAiB,EAAE,OAAyB;QACpE,MAAM,OAAO,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACxC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7B,OAAO,OAAO,CAAC,aAAa,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,IAAiB,EAAE,OAAyB;QACxD,MAAM,SAAS,GAAG,CAAC,IAAiB,EAAE,EAAE;YACtC,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;gBAC5D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC3B;YACD,kBAAU,CAAC,IAAI,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC3E,CAAC,CAAC;QACF,SAAS,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;CACF;AAED,MAAM,oBAAoB;IAA1B;QACU,sBAAiB,GAAG,KAAK,CAAC;IAkCpC,CAAC;IAhCC,MAAM,CAAC,wBAAwB,CAC7B,IAAiB,EACjB,KAAwB,EACxB,OAAyB;QAEzB,MAAM,OAAO,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC3C,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC,iBAAiB,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,IAAiB,EAAE,KAAwB,EAAE,OAAyB;QAClF,MAAM,SAAS,GAAG,CAAC,IAAiB,EAAE,EAAE;;YACtC,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,OAAO;aACR;YACD,MAAM,CAAC,GAAG,IAAqB,CAAC;YAChC,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAkB,IAAI,CAAA,MAAA,CAAC,CAAC,MAAM,0CAAE,IAAI,MAAK,kBAAkB,EAAE;gBAC1E,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC/B,IACE,MAAM,CAAC,IAAI,KAAK,YAAY;oBAC5B,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;oBAC1B,QAAQ,CAAC,IAAI,KAAK,YAAY;oBAC9B,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAC1B;oBACA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC9B,OAAO;iBACR;aACF;YACD,kBAAU,CAAC,IAAI,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC3E,CAAC,CAAC;QACF,SAAS,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;CACF"}