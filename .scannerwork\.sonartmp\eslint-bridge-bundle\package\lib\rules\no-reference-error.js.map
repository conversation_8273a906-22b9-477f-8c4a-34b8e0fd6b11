{"version": 3, "file": "no-reference-error.js", "sourceRoot": "", "sources": ["../../src/rules/no-reference-error.ts"], "names": [], "mappings": ";;;AAuBA,oCAAuE;AAE1D,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAChC,MAAM,2BAA2B,GAAqC,IAAI,GAAG,EAAE,CAAC;QAChF,OAAO;YACL,cAAc;gBACZ,aAAa,CAAC,KAAK,EAAE,CAAC;gBACtB,2BAA2B,CAAC,KAAK,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACvC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAChC,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;oBAClC,IAAI,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;wBACtC,OAAO;qBACR;oBACD,IACE,GAAG,CAAC,SAAS;wBACb,iBAAiB,CAAC,UAA2B,CAAC;wBAC9C,qBAAqB,CAAC,UAA2B,CAAC,EAClD;wBACA,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;wBACnC,OAAO;qBACR;oBACD,MAAM,sBAAsB,GAAG,2BAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBAChF,IAAI,CAAC,CAAC,sBAAsB,EAAE;wBAC5B,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACzC;yBAAM;wBACL,2BAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;qBAChE;gBACH,CAAC,CAAC,CAAC;gBACH,2BAA2B,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE;oBACxD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;wBACpB,OAAO,EAAE,wBAAgB,CACvB,KAAK,IAAI,4GAA4G,EACrH,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CACrB;qBACF,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,qBAAqB,CAAC,IAAmB;IAChD,OAAO,CAAC,CAAC,iCAAyB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC;AAC1F,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAmB;IAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,OAAO,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,iBAAiB,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAC5E,CAAC"}