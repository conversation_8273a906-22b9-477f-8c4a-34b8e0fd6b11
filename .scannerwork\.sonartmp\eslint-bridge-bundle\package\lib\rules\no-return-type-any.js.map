{"version": 3, "file": "no-return-type-any.js", "sourceRoot": "", "sources": ["../../src/rules/no-return-type-any.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAIjD,oCAA4E;AAE5E,+CAAiC;AAIjC,MAAM,OAAO,GAAG,0DAA0D,CAAC;AAE9D,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QAExC,IAAI,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACtC,MAAM,mBAAmB,GAA2B,EAAE,CAAC;YACvD,OAAO;gBACL,eAAe,CAAC,IAAiB;oBAC/B,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;wBAClC,mBAAmB,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CACrD,IAA+B,CAAC,QAAQ,CAC1C,CAAC;qBACH;gBACH,CAAC;gBACD,mBAAmB,EAAE;oBACnB,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC/B,CAAC;gBACD,0BAA0B,EAAE,UAAU,IAAiB;oBACrD,MAAM,UAAU,GAAI,IAAqC,CAAC,UAAU,CAAC;oBACrE,IACE,UAAU;wBACV,UAAU,CAAC,cAAc,CAAC,IAAI,KAAK,cAAc;wBACjD,mBAAmB,CAAC,MAAM,GAAG,CAAC;wBAC9B,mBAAmB,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,EAClF;wBACA,OAAO,CAAC,MAAM,CAAC;4BACb,OAAO;4BACP,GAAG,EAAE,UAAU,CAAC,GAAG;yBACpB,CAAC,CAAC;qBACJ;oBACD,mBAAmB,CAAC,GAAG,EAAE,CAAC;gBAC5B,CAAC;aACF,CAAC;SACH;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAC;AAEF,SAAS,mBAAmB,CAC1B,OAA6B,EAC7B,QAAgC;IAEhC,MAAM,eAAe,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;IACrE,IAAI,CAAC,CAAC,eAAe,IAAI,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,EAAE;QAC3D,OAAO,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YAChC,MAAM,cAAc,GAAG,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACjE,OAAO,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,CAAC;QAC5E,CAAC,CAAC,CAAC;KACJ;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAwB,EAAE,QAAgC;IACrF,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IAClD,OAAO,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAqB,CAAC,CAAC,CAAC;AAC9F,CAAC;AAED,SAAS,eAAe,CAAC,EAAE,KAAK,EAAW;IACzC,OAAO,CACL,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,WAAW;QAChC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU;QAC/B,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU;QAC/B,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAC9B,CAAC;AACJ,CAAC"}