{"version": 3, "file": "disabled-auto-escaping.js", "sourceRoot": "", "sources": ["../../src/rules/disabled-auto-escaping.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAQkB;AAElB,MAAM,OAAO,GAAG,yDAAyD,CAAC;AAE7D,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QAExC,SAAS,wBAAwB,CAC/B,iBAGkC;YAElC,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzC,OAAO,KAAK,CAAC;aACd;YACD,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/C,IAAI,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE;gBACpC,OAAO,KAAK,CAAC;aACd;YACD,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC;YACvC,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE;gBACpD,OAAO,CACL,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY;oBAC5C,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,CAC/C,CAAC;aACH;YACD,MAAM,EAAE,IAAI,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC;YACxC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrB,OAAO,KAAK,CAAC;aACd;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,IACE,aAAa,CAAC,IAAI,KAAK,iBAAiB;gBACxC,aAAa,CAAC,QAAQ;gBACtB,oBAAY,CAAC,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,EACpD;gBACA,OAAO,IAAI,CAAC;aACb;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,0BAA0B,CAAC,IAAiB;;YAOnD,IAAI,gBAAgB,GAClB,MAAA,4BAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,oBAAoB,CAAC,mCACzD,4BAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAAC;YACjE,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,gCAAwB,CAAC,QAAQ,CAAC,EAAE;gBACzF,gBAAgB,GAAG,oCAA4B,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;aAChE;YACD,IAAI,CAAC,CAAC,gBAAgB,EAAE;gBACtB,OAAO,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;aACnD;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,cAAc,GAAG,IAA6B,CAAC;gBACrD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,SAAS,CAAC,EAAE;oBACjE,0BAAkB,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;iBAC3E;gBACD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,YAAY,CAAC,EAAE;oBAChE,0BAAkB,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;iBAC5E;gBACD,MAAM,YAAY,GAAG,2BAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;gBACzE,IAAI,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,MAAK,aAAa,EAAE;oBACzC,0BAAkB,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;iBACvE;YACH,CAAC;YACD,aAAa,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACnC,MAAM,aAAa,GAAG,IAA4B,CAAC;gBACnD,MAAM,EAAE,MAAM,EAAE,GAAG,aAAa,CAAC;gBACjC,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;oBACtC,OAAO;iBACR;gBACD,MAAM,MAAM,GAAG,2BAAmB,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC3D,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,MAAK,QAAQ,IAAI,oBAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE;oBAC3E,0BAAkB,CAAC,OAAO,EAAE,aAAa,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;iBAC3E;YACH,CAAC;YACD,oBAAoB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC1C,MAAM,oBAAoB,GAAG,IAAmC,CAAC;gBACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,oBAAoB,CAAC;gBAC7C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE;oBACpC,OAAO;iBACR;gBACD,MAAM,MAAM,GAAG,2BAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzD,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,MAAK,UAAU,IAAI,CAAC,oBAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;oBAC1E,OAAO;iBACR;gBACD,IAAI,0BAA0B,CAAC,KAAK,CAAC,EAAE;oBACrC,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;iBAClD;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}