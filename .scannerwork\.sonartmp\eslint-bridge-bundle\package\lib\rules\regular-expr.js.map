{"version": 3, "file": "regular-expr.js", "sourceRoot": "", "sources": ["../../src/rules/regular-expr.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAA8D;AAE9D,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACnD,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAErC,MAAM,OAAO,GAAG,yDAAyD,CAAC;AAE7D,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,OAAO,CAAC,IAAiB;gBACvB,MAAM,EAAE,KAAK,EAAE,GAAG,IAA4B,CAAC;gBAC/C,IAAI,KAAK,EAAE;oBACT,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;oBAC1B,IAAI,oBAAoB,CAAC,OAAO,CAAC,EAAE;wBACjC,OAAO,CAAC,MAAM,CAAC;4BACb,OAAO;4BACP,IAAI;yBACL,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;YAED,cAAc,CAAC,IAAiB;gBAC9B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAA6B,CAAC;gBAClE,IAAI,4BAAoB,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC,EAAE;oBAClD,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBACnC;YACH,CAAC;YAED,aAAa,CAAC,IAAiB;gBAC7B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAA4B,CAAC;gBACjE,IAAI,oBAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;oBAClC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBACnC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,kBAAkB,CAAC,IAAmB,EAAE,OAAyB;IACxE,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB,IACE,QAAQ;QACR,QAAQ,CAAC,IAAI,KAAK,SAAS;QAC3B,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;QAClC,oBAAoB,CAAC,QAAQ,CAAC,KAAK,CAAC,EACpC;QACA,OAAO,CAAC,MAAM,CAAC;YACb,OAAO;YACP,IAAI,EAAE,QAAQ;SACf,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,KAAa;IACzC,OAAO,KAAK,CAAC,MAAM,IAAI,gBAAgB,IAAI,6BAA6B,CAAC,KAAK,CAAC,CAAC;AAClF,CAAC;AAED,SAAS,6BAA6B,CAAC,KAAa;IAClD,IAAI,oBAAoB,GAAG,CAAC,CAAC;IAC7B,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;QACrB,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5B,oBAAoB,EAAE,CAAC;SACxB;QACD,IAAI,oBAAoB,KAAK,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}