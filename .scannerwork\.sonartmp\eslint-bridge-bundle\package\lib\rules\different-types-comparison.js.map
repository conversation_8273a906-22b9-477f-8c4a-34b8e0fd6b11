{"version": 3, "file": "different-types-comparison.js", "sourceRoot": "", "sources": ["../../src/rules/different-types-comparison.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAIjD,+CAAiC;AACjC,oCAA2F;AAE9E,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QAED,SAAS,YAAY,CAAC,CAAU,EAAE,CAAU;YAC1C,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;QACjE,CAAC;QAED,SAAS,SAAS,CAAC,CAAU,EAAE,CAAU;YACvC,OAAO,CACL,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;gBACzB,CAAC,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CACpE,CAAC;QACJ,CAAC;QAED,SAAS,KAAK,CAAC,IAAa;YAC1B,OAAO,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,CAAC;QAED,SAAS,iBAAiB,CAAC,IAAa;YACtC,OAAO,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC;QACnF,CAAC;QAED,SAAS,MAAM,CAAC,IAAiB;YAC/B,OAAO,IAAI,CAAC,IAAI,KAAK,gBAAgB,CAAC;QACxC,CAAC;QAED,SAAS,mBAAmB,CAAC,GAAgB,EAAE,GAAgB;YAC7D,MAAM,EAAE,wBAAwB,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YACvE,MAAM,OAAO,GAAG,wBAAwB,CAAC,2BAAmB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC7E,MAAM,OAAO,GAAG,wBAAwB,CAAC,2BAAmB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC7E,OAAO,CACL,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC;gBAC/B,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;gBAC5B,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;gBAC5B,CAAC,KAAK,CAAC,OAAO,CAAC;gBACf,CAAC,KAAK,CAAC,OAAO,CAAC;gBACf,CAAC,iBAAiB,CAAC,OAAO,CAAC;gBAC3B,CAAC,iBAAiB,CAAC,OAAO,CAAC;gBAC3B,CAAC,MAAM,CAAC,GAAG,CAAC;gBACZ,CAAC,MAAM,CAAC,GAAG,CAAC,CACb,CAAC;QACJ,CAAC;QAED,OAAO;YACL,gBAAgB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACtC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,IAA+B,CAAC;gBAClE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;oBACzE,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,GAC/B,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;oBACtE,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,wBAAgB,CACvB,gBAAgB,MAAM,8BAA8B,OAAO,0BAA0B,QAAQ,IAAI,EACjG,CAAC,IAAI,EAAE,KAAK,CAAC,CACd;wBACD,GAAG,EAAE,OAAO;6BACT,aAAa,EAAE;6BACf,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;6BAC7B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAE,CAAC,GAAG;qBAC/E,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}