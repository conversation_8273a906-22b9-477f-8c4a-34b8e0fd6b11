{"version": 3, "file": "non-existent-operator.js", "sourceRoot": "", "sources": ["../../src/rules/non-existent-operator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKpC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,oBAAoB,CAAC,IAAiB;gBACpC,MAAM,oBAAoB,GAAG,IAAmC,CAAC;gBACjE,IAAI,oBAAoB,CAAC,QAAQ,KAAK,GAAG,EAAE;oBACzC,aAAa,CAAC,OAAO,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAC;iBACpD;YACH,CAAC;YACD,kBAAkB,CAAC,IAAiB;gBAClC,MAAM,kBAAkB,GAAG,IAAiC,CAAC;gBAC7D,aAAa,CAAC,OAAO,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,aAAa,CAAC,OAAyB,EAAE,SAAoC;IACpF,IACE,SAAS;QACT,SAAS,CAAC,IAAI,KAAK,iBAAiB;QACpC,yBAAyB,CAAC,SAAS,CAAC,QAAQ,CAAC,EAC7C;QACA,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,uBAAuB,GAAG,UAAU,CAAC,cAAc,CACvD,SAAS,EACT,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAC7B,CAAC;QACF,MAAM,kBAAkB,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAC/D,MAAM,oBAAoB,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE1E,IACE,uBAAuB,IAAI,IAAI;YAC/B,kBAAkB,IAAI,IAAI;YAC1B,oBAAoB,IAAI,IAAI;YAC5B,WAAW,CAAC,uBAAuB,EAAE,kBAAkB,CAAC;YACxD,CAAC,WAAW,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,EACtD;YACA,OAAO,CAAC,MAAM,CAAC;gBACb,OAAO,EAAE,QAAQ,SAAS,CAAC,QAAQ,mBAAmB;gBACtD,GAAG,EAAE,EAAE,KAAK,EAAE,uBAAuB,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE;aACnF,CAAC,CAAC;SACJ;KACF;AACH,CAAC;AAED,SAAS,yBAAyB,CAAC,QAA8B;IAC/D,OAAO,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,CAAC;AAClE,CAAC;AAED,SAAS,WAAW,CAAC,KAAgB,EAAE,MAAiB;IACtD,OAAO,CACL,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CACjG,CAAC;AACJ,CAAC"}