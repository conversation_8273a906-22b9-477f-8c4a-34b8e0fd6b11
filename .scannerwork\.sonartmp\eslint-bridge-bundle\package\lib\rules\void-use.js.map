{"version": 3, "file": "void-use.js", "sourceRoot": "", "sources": ["../../src/rules/void-use.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKpC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,SAAS,SAAS,CAAC,IAAiB;YAClC,MAAM,eAAe,GAA2B,IAA8B,CAAC;YAC/E,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC,eAAe,CAAC,EAAE;gBACvD,OAAO;aACR;YACD,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,cAAc,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACvF,OAAO,CAAC,MAAM,CAAC;gBACb,GAAG,EAAE,aAAc,CAAC,GAAG;gBACvB,OAAO,EAAE,yCAAyC;aACnD,CAAC,CAAC;QACL,CAAC;QAED,SAAS,OAAO,CAAC,IAA4B;YAC3C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACvE,CAAC;QAED,SAAS,MAAM,CAAC,IAA4B;YAC1C,OAAO,CACL,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,gBAAgB;gBACvC,CAAC,yBAAyB,EAAE,oBAAoB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CACtF,CAAC;QACJ,CAAC;QAED,OAAO;YACL,kCAAkC,EAAE,SAAS;SAC9C,CAAC;IACJ,CAAC;CACF,CAAC"}