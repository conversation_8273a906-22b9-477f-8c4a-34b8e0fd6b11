{"version": 3, "file": "no-intrusive-permissions.js", "sourceRoot": "", "sources": ["../../src/rules/no-intrusive-permissions.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAAkF;AAElF,MAAM,WAAW,GAAG,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe,EAAE,oBAAoB,CAAC,CAAC;AAEtF,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,gDAAgD,CAAC,IAAiB;gBAChE,MAAM,IAAI,GAAG,IAA6B,CAAC;gBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAiC,CAAC;gBACtD,IACE,2BAA2B,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC;oBAC3D,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EACzB;oBACA,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBAChC,OAAO;iBACR;gBACD,IACE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;oBACvC,2BAA2B,CAAC,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,oBAAoB,CAAC,EACzF;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,oDAAoD;wBAC7D,IAAI,EAAE,MAAM;qBACb,CAAC,CAAC;oBACH,OAAO;iBACR;gBACD,IACE,2BAA2B,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,CAAC;oBACnE,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EACzB;oBACA,MAAM,QAAQ,GAAG,4BAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;oBACtF,sCAAsC,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;oBAClE,OAAO;iBACR;gBACD,IACE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;oBACzC,0BAAkB,CAAC,MAAM,EAAE,cAAc,EAAE,mBAAmB,CAAC,EAC/D;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,sDAAsD;wBAC/D,IAAI,EAAE,MAAM;qBACb,CAAC,CAAC;oBACH,OAAO;iBACR;gBACD,IACE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC;oBAC9C,0BAAkB,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC,EACzD;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,2DAA2D;wBACpE,IAAI,EAAE,MAAM;qBACb,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,aAAa,CAAC,IAAiB;gBAC7B,MAAM,EAAE,MAAM,EAAE,GAAG,IAA4B,CAAC;gBAChD,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,oBAAY,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE;oBACrF,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,sDAAsD;wBAC/D,IAAI,EAAE,MAAM;qBACb,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,sCAAsC,CAC7C,OAAyB,EACzB,MAA+B,EAC/B,QAA6C;IAE7C,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO;KACR;IACD,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAChE,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5D,IAAI,CAAC,gBAAgB,IAAI,CAAC,gBAAgB,EAAE;QAC1C,OAAO;KACR;IACD,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,UAAU,EAAE;QACtC,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;YAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAC5B,IAAI,oBAAY,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;gBACtF,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC1B;iBAAM,IACL,oBAAY,CAAC,GAAG,EAAE,OAAO,CAAC;gBAC1B,gBAAgB;gBAChB,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,EAChC;gBACA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACtB;SACF;KACF;IACD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACpB,OAAO,CAAC,MAAM,CAAC;YACb,OAAO,EAAE,4BAA4B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB;YACxE,IAAI,EAAE,MAAM;SACb,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAyB,EAAE,KAAkB;IACrE,MAAM,SAAS,GAAG,4BAAoB,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IAClE,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,EAAE;QAC1C,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAyB,EAAE,IAA2B;IAC9E,MAAM,QAAQ,GAAG,4BAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;IACtF,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,MAAK,kBAAkB,EAAE;QACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,6BAA6B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;QAChG,IAAI,QAAQ,EAAE;YACZ,MAAM,EAAE,KAAK,EAAE,GAAI,QAA4B,CAAC,KAAuB,CAAC;YACxE,OAAO,CAAC,MAAM,CAAC;gBACb,OAAO,EAAE,4BAA4B,KAAK,gBAAgB;gBAC1D,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;SACJ;KACF;AACH,CAAC;AAED,SAAS,2BAA2B,CAClC,EAAE,MAAM,EAAE,QAAQ,EAA2B,EAC7C,aAAqB,EACrB,GAAG,cAAwB;IAE3B,OAAO,CACL,0BAAkB,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,CAAC;QACtD,oBAAY,CAAC,QAAQ,EAAE,GAAG,cAAc,CAAC,CAC1C,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CACpC,IAA4C,EAC5C,OAAyB;IAEzB,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,oBAAY,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,KAAK,GAAG,4BAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACnE,OAAO,CACL,KAAK;YACL,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;YAC/B,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;YACjC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CACtC,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}