{"version": 3, "file": "csrf.js", "sourceRoot": "", "sources": ["../../src/rules/csrf.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAGjD,yEAA6E;AAE7E,oCAQkB;AAElB,MAAM,YAAY,GAAG,OAAO,CAAC;AAC7B,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;AAEnC,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,IAAI,oBAAoB,GAAG,KAAK,CAAC;QACjC,IAAI,sBAAsB,GAAG,KAAK,CAAC;QAEnC,SAAS,mBAAmB,CAAC,IAAqB;YAChD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE;gBACzC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC7B,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ;qBACrC,MAAM,CAAC,iBAAS,CAAC;qBACjB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC/E,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC5B,MAAM,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,aAAa,CAAC;oBACvC,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,4BAAgB,CAAC,mDAAmD,EAAE,IAAI,CAAC;wBACpF,IAAI,EAAE,KAAK;qBACZ,CAAC,CAAC;iBACJ;aACF;QACH,CAAC;QAED,SAAS,iBAAiB,CAAC,IAA6B;YACtD,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,MAAK,YAAY,EAAE;gBAC/B,IAAI,GAAG,2BAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aAChD;YAED,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE;gBAC/E,MAAM,MAAM,GAAG,iCAAyB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/D,OAAO,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,MAAK,YAAY,CAAC;aACvC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,mBAAmB,CAAC,cAAqC;YAChE,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;YAElC,mBAAmB;YACnB,MAAM,cAAc,GAAG,gCAAwB,CAAC,cAAc,CAAC,CAAC;YAChE,IAAI,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,KAAK,MAAK,YAAY,EAAE;gBAC1C,sBAAsB,GAAG,IAAI,CAAC;aAC/B;YAED,aAAa;YACb,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE;gBAChC,MAAM,UAAU,GAAG,iCAAyB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAE9D,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,MAAK,YAAY,EAAE;oBACtC,MAAM,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,SAAS,CAAC;oBACxC,MAAM,cAAc,GAAG,mCAA2B,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;oBAC1E,IAAI,cAAc,EAAE;wBAClB,mBAAmB,CAAC,cAAc,CAAC,CAAC;qBACrC;iBACF;aACF;YAED,sBAAsB;YACtB,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;gBACtC,IACE,oBAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;oBACpC,mBAAW,CAAC,OAAO,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACtE;oBACA,oBAAoB,GAAG,IAAI,CAAC;iBAC7B;gBACD,IACE,oBAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;oBAC/D,CAAC,oBAAoB;oBACrB,sBAAsB;oBACtB,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAC7D;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,4BAAgB,CAAC,mDAAmD,EAAE,EAAE,CAAC;wBAClF,IAAI,EAAE,MAAM;qBACb,CAAC,CAAC;iBACJ;aACF;QACH,CAAC;QAED,OAAO;YACL,OAAO;gBACL,oBAAoB,GAAG,KAAK,CAAC;YAC/B,CAAC;YACD,cAAc,CAAC,IAAiB;gBAC9B,mBAAmB,CAAC,IAA6B,CAAC,CAAC;YACrD,CAAC;YACD,iBAAiB,CAAC,IAAiB;gBACjC,IAAK,IAAiC,CAAC,MAAM,CAAC,KAAK,KAAK,YAAY,EAAE;oBACpE,sBAAsB,GAAG,IAAI,CAAC;iBAC/B;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}