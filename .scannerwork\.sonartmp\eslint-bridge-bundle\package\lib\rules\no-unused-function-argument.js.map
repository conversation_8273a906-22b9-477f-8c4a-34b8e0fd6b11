{"version": 3, "file": "no-unused-function-argument.js", "sourceRoot": "", "sources": ["../../src/rules/no-unused-function-argument.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAMpC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,yCAAyC,EAAE,UAAU,IAAiB;gBACpE,oBAAoB,CAClB,IAAI,EACH,IAA+D,CAAC,EAAE,EACnE,OAAO,CACR,CAAC;YACJ,CAAC;YACD,uBAAuB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC7C,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YACjD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,oBAAoB,CAC3B,IAAiB,EACjB,UAAgD,EAChD,OAAyB;IAEzB,MAAM,MAAM,GAAI,IAAsB,CAAC,MAAM,CAAC;IAC9C,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;QACjE,OAAO;KACR;IAED,IACE,OAAO;SACJ,QAAQ,EAAE;SACV,SAAS,CAAC,IAAI,CACb,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CACrF,EACH;QACA,OAAO;KACR;IAED,IAAI,kBAAkB,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAE5D,IAAI,UAAU,EAAE;QACd,kBAAkB,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC;KACjF;IAED,IAAI,CAAC,GAAG,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;IACtC,OAAO,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE;QACxD,OAAO,CAAC,MAAM,CAAC;YACb,OAAO,EAAE,yCAAyC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;YAChF,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;SAC3C,CAAC,CAAC;QACH,CAAC,EAAE,CAAC;KACL;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAwB;IAChD,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC;IACjC,2FAA2F;IAC3F,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAClE,CAAC"}