{"version": 3, "file": "unverified-hostname.js", "sourceRoot": "", "sources": ["../../src/rules/unverified-hostname.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAOkB;AAEL,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,OAAO,GAAG,iEAAiE,CAAC;QAClF,MAAM,iBAAiB,GAAG,qCAAqC,CAAC;QAChE,SAAS,sBAAsB,CAC7B,cAAqC,EACrC,sBAA8B;YAE9B,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,sBAAsB,GAAG,CAAC,EAAE;gBAChE,OAAO;aACR;YACD,MAAM,iBAAiB,GAAG,cAAc,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;YAC3E,MAAM,kBAAkB,GAAkB,EAAE,CAAC;YAC7C,MAAM,iBAAiB,GAA2B,EAAE,CAAC;YACrD,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,MAAM,aAAa,GAAG,4BAAoB,CAAC,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;YAC3F,IAAI,CAAC,aAAa,EAAE;gBAClB,OAAO;aACR;YACD,IAAI,iBAAiB,KAAK,aAAa,EAAE;gBACvC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACnC;YACD,MAAM,qCAAqC,GAAG,4BAAoB,CAChE,OAAO,EACP,aAAa,EACb,oBAAoB,EACpB,KAAK,CACN,CAAC;YACF,IAAI,qCAAqC,EAAE;gBACzC,kBAAkB,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBAC/D,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC1C,YAAY,GAAG,IAAI,CAAC;aACrB;YACD,MAAM,2BAA2B,GAAG,mCAA2B,CAC7D,aAAa,EACb,qBAAqB,CACtB,CAAC;YACF,IACE,2BAA2B;gBAC3B,yCAAyC,CAAC,2BAA2B,CAAC,EACtE;gBACA,kBAAkB,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBACrD,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClC,YAAY,GAAG,IAAI,CAAC;aACrB;YACD,IAAI,YAAY,EAAE;gBAChB,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,cAAc,CAAC,MAAM;oBAC3B,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;iBAC1E,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,yCAAyC,CAChD,2BAA4C;YAE5C,IAAI,YAA6C,CAAC;YAClD,YAAY,GAAG,4BAAoB,CACjC,OAAO,EACP,2BAA2B,CAAC,KAAK,EACjC,oBAAoB,CACrB,CAAC;YACF,IAAI,CAAC,YAAY,EAAE;gBACjB,YAAY,GAAG,4BAAoB,CACjC,OAAO,EACP,2BAA2B,CAAC,KAAK,EACjC,yBAAyB,CAC1B,CAAC;aACH;YACD,IAAI,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,CAAC,IAAI,MAAK,gBAAgB,EAAE;gBAChD,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,mBAAmB,CAClE,YAAY,CAAC,IAAI,EACjB,OAAO,CACR,CAAC;gBACF,IACE,gBAAgB,CAAC,MAAM,KAAK,CAAC;oBAC7B,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;;wBACzB,OAAO,CACL,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAA,MAAA,4BAAoB,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,0CAAE,KAAK,MAAK,IAAI,CACpF,CAAC;oBACJ,CAAC,CAAC,EACF;oBACA,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,cAAc,GAAG,IAA6B,CAAC;gBACrD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE;oBAC5D,sBAAsB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;iBAC3C;gBACD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE;oBAC1D,sBAAsB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;iBAC3C;gBACD,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE;oBAC1D,sBAAsB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;iBAC3C;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,MAAM,uBAAuB;IAA7B;QACmB,qBAAgB,GAA6B,EAAE,CAAC;IAuBnE,CAAC;IArBC,MAAM,CAAC,mBAAmB,CAAC,IAAiB,EAAE,OAAyB;QACrE,MAAM,OAAO,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAC9C,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7B,OAAO,OAAO,CAAC,gBAAgB,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,IAAiB,EAAE,OAAyB;QACxD,MAAM,SAAS,GAAG,CAAC,IAAiB,EAAE,EAAE;YACtC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,iBAAiB;oBACpB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjC,MAAM;gBACR,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,oBAAoB,CAAC;gBAC1B,KAAK,yBAAyB;oBAC5B,OAAO;aACV;YACD,kBAAU,CAAC,IAAI,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC3E,CAAC,CAAC;QACF,SAAS,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;CACF"}