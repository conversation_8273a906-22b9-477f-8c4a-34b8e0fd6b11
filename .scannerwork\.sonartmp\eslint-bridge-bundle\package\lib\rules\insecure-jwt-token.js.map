{"version": 3, "file": "insecure-jwt-token.js", "sourceRoot": "", "sources": ["../../src/rules/insecure-jwt-token.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAOkB;AAEL,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,YAAY,GAAG,0DAA0D,CAAC;QAChF,MAAM,cAAc,GAClB,6EAA6E,CAAC;QAChF,MAAM,iBAAiB,GAAG,0EAA0E,CAAC;QAErG,SAAS,eAAe,CACtB,cAAqC,EACrC,kBAA2C,EAC3C,kBAAiC;YAEjC,MAAM,uBAAuB,GAAG,4BAAoB,CAClD,OAAO,EACP,kBAAkB,EAClB,WAAW,EACX,MAAM,CACP,CAAC;YACF,IAAI,uBAAuB,EAAE;gBAC3B,MAAM,oBAAoB,GAAG,4BAAoB,CAC/C,OAAO,EACP,uBAAuB,CAAC,KAAK,EAC7B,SAAS,CACV,CAAC;gBACF,IAAI,oBAAoB,IAAI,oBAAoB,KAAK,uBAAuB,CAAC,KAAK,EAAE;oBAClF,kBAAkB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;iBAC/C;gBACD,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;aACvE;QACH,CAAC;QAED,SAAS,iBAAiB,CACxB,cAAqC,EACrC,SAAsB,EACtB,kBAA2C,EAC3C,kBAAiC;YAEjC,MAAM,kBAAkB,GAAG,mCAA2B,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;YACzF,IAAI,CAAC,kBAAkB,EAAE;gBACvB,IAAI,qBAAa,CAAC,SAAS,CAAC,EAAE;oBAC5B,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;iBACzE;gBACD,OAAO;aACR;YACD,MAAM,eAAe,GAAG,4BAAoB,CAC1C,OAAO,EACP,kBAAkB,CAAC,KAAK,EACxB,iBAAiB,CAClB,CAAC;YACF,IAAI,CAAC,eAAe,EAAE;gBACpB,OAAO;aACR;YACD,MAAM,qBAAqB,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;gBAC9D,MAAM,KAAK,GAAG,4BAAoB,CAAC,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;gBAC1D,OAAO,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,KAAK,MAAK,MAAM,CAAC;YACjC,CAAC,CAAC,CAAC;YACH,IAAI,qBAAqB,EAAE;gBACzB,IAAI,kBAAkB,CAAC,KAAK,KAAK,eAAe,EAAE;oBAChD,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;iBAC1C;gBACD,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;aACzE;QACH,CAAC;QAED,SAAS,YAAY,CAAC,IAAiB,EAAE,OAAe,EAAE,kBAAiC;YACzF,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,OAAO,EAAE,wBAAgB,CACvB,OAAO,EACP,kBAAkB,EAClB,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CACzD;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,cAAc,GAA0B,IAA6B,CAAC;gBAC5E,MAAM,YAAY,GAAG,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;gBAClF,MAAM,cAAc,GAAG,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;gBACtF,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,EAAE;oBACpC,OAAO;iBACR;gBACD,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvC,oFAAoF;oBACpF,OAAO;iBACR;gBACD,MAAM,aAAa,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAClD,MAAM,kBAAkB,GAAG,4BAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,kBAAkB,CAAC,CAAC;gBAC5F,IAAI,CAAC,kBAAkB,EAAE;oBACvB,OAAO;iBACR;gBACD,MAAM,kBAAkB,GAAkB,CAAC,kBAAkB,CAAC,CAAC;gBAC/D,IAAI,kBAAkB,KAAK,aAAa,EAAE;oBACxC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;iBACxC;gBACD,IAAI,YAAY,EAAE;oBAChB,eAAe,CAAC,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;iBACzE;gBACD,MAAM,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACnD,IAAI,cAAc,EAAE;oBAClB,iBAAiB,CAAC,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;iBAC3F;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}