{"version": 3, "file": "os-command.js", "sourceRoot": "", "sources": ["../../src/rules/os-command.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAIkB;AAElB,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAE5C,MAAM,yBAAyB,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AAErF,MAAM,oBAAoB,GAAG,eAAe,CAAC;AAE7C,MAAM,OAAO,GAAG,wDAAwD,CAAC;AAI5D,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE,CACpC,mBAAmB,CAAC,IAA6B,EAAE,OAAO,CAAC;SAC9D,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,mBAAmB,CAC1B,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAyB,EAClD,OAAyB;IAEzB,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACtC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE;YACvC,MAAM,UAAU,GAAG,iCAAyB,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACrE,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SAC5D;KACF;SAAM,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE;QACvC,MAAM,UAAU,GAAG,yCAAiC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACtE,cAAc,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KACnD;AACH,CAAC;AAED,SAAS,cAAc,CACrB,UAAsC,EACtC,MAAyB,EACzB,IAAgB,EAChB,OAAyB;IAEzB,IAAI,UAAU,IAAI,UAAU,CAAC,KAAK,KAAK,oBAAoB,IAAI,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;QAC3F,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,cAAc,CAAC,UAA6B,EAAE,CAAC,OAAO,EAAE,GAAG,cAAc,CAAa;IAC7F,sCAAsC;IACtC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;QAC1C,OAAO,KAAK,CAAC;KACd;IACD,mEAAmE;IACnE,IAAI,oBAAY,CAAC,UAAU,EAAE,GAAG,yBAAyB,CAAC,EAAE;QAC1D,OAAO,mBAAmB,CAAC,cAAc,CAAC,CAAC;KAC5C;IACD,OAAO,oBAAY,CAAC,UAAU,EAAE,GAAG,cAAc,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,mBAAmB,CAAC,cAA0B;IACrD,OAAO,cAAc,CAAC,IAAI,CACxB,GAAG,CAAC,EAAE,CACJ,GAAG,CAAC,IAAI,KAAK,kBAAkB;QAC9B,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAuB,CAAC,IAAI,CAC3E,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,CACjB,oBAAY,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CACjF,CACJ,CAAC;AACJ,CAAC"}