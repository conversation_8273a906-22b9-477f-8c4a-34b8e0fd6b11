{"version": 3, "file": "file-uploads.js", "sourceRoot": "", "sources": ["../../src/rules/file-uploads.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAGjD,yEAA6E;AAE7E,oCAOkB;AAElB,MAAM,iBAAiB,GAAG,YAAY,CAAC;AACvC,MAAM,eAAe,GAAG,gBAAgB,CAAC;AACzC,MAAM,UAAU,GAAG,WAAW,CAAC;AAE/B,MAAM,aAAa,GAAG,QAAQ,CAAC;AAC/B,MAAM,cAAc,GAAG,SAAS,CAAC;AACjC,MAAM,kBAAkB,GAAG,aAAa,CAAC;AAEzC,MAAM,iBAAiB,GAGnB,IAAI,GAAG,EAAE,CAAC;AAED,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,aAAa,CAAC,IAAiB;gBAC7B,mBAAmB,CAAC,OAAO,EAAE,IAA4B,CAAC,CAAC;YAC7D,CAAC;YACD,cAAc,CAAC,IAAiB;gBAC9B,mBAAmB,CAAC,OAAO,EAAE,IAA6B,CAAC,CAAC;YAC9D,CAAC;YACD,oBAAoB,CAAC,IAAiB;gBACpC,eAAe,CAAC,OAAO,EAAE,IAAmC,CAAC,CAAC;YAChE,CAAC;YACD,OAAO;gBACL,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC5B,CAAC;YACD,cAAc;gBACZ,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAChC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,cAAc,CAAC,CAChF,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,mBAAmB,CAAC,OAAyB,EAAE,cAAqC;IAC3F,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;IAElC,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE;QAChC,OAAO;KACR;IAED,MAAM,UAAU,GACd,yCAAiC,CAAC,OAAO,EAAE,MAAM,CAAC;QAClD,iCAAyB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAE7C,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,MAAK,iBAAiB,EAAE;QAC3C,eAAe,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;KAC1C;IAED,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,MAAK,aAAa,EAAE;QACvC,WAAW,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;KACtC;AACH,CAAC;AAED,SAAS,eAAe,CAAC,OAAyB,EAAE,cAAqC;;IACvF,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QACzC,MAAM,YAAY,GAAG,sBAAc,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,YAAY,EAAE;YAChB,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE;gBAClC,YAAY,EAAE,KAAK;gBACnB,cAAc,EAAE,KAAK;gBACrB,cAAc;aACf,CAAC,CAAC;SACJ;QACD,OAAO;KACR;IAED,MAAM,OAAO,GAAG,4BAAoB,CAAC,OAAO,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;IAC/F,IAAI,OAAO,EAAE;QACX,MAAM,CACJ,OAAO,EACP,CAAC,CAAC,mCAA2B,CAAC,OAAO,EAAE,UAAU,CAAC,EAClD,mBAAmB,CAAC,MAAA,mCAA2B,CAAC,OAAO,EAAE,eAAe,CAAC,0CAAE,KAAK,CAAC,EACjF,cAAc,CACf,CAAC;KACH;AACH,CAAC;AAED,SAAS,WAAW,CAAC,OAAyB,EAAE,cAAqC;;IACnF,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QACzC,OAAO;KACR;IACD,MAAM,aAAa,GAAG,4BAAoB,CACxC,OAAO,EACP,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAC3B,kBAAkB,CACnB,CAAC;IAEF,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IAED,MAAM,oBAAoB,GAAG,MAAA,mCAA2B,CAAC,aAAa,EAAE,cAAc,CAAC,0CAAE,KAAK,CAAC;IAC/F,IAAI,oBAAoB,EAAE;QACxB,MAAM,YAAY,GAAG,4BAAoB,CAAC,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;QAE3F,IAAI,YAAY,EAAE;YAChB,MAAM,iBAAiB,GAAG,mCAAmC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACrF,IAAI,iBAAiB,EAAE;gBACrB,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE;oBAC5C,IAAI,EAAE,iBAAiB;oBACvB,OAAO,EAAE,0BAA0B;iBACpC,CAAC,CAAC;aACJ;SACF;KACF;AACH,CAAC;AAED,SAAS,mCAAmC,CAC1C,OAAyB,EACzB,eAAsC;IAEtC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,eAAe,CAAC;IACpD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,oBAAoB,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE;QAClE,MAAM,cAAc,GAAG,4BAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAClF,IAAI,cAAc,IAAI,CAAC,mCAA2B,CAAC,cAAc,EAAE,kBAAkB,CAAC,EAAE;YACtF,OAAO,MAAM,CAAC;SACf;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAiB,EAAE,QAAgB;IAC/D,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,kBAAkB;QAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;QACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAChC,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,cAA4B;IACvD,IACE,cAAc;QACd,cAAc,CAAC,IAAI,KAAK,SAAS;QACjC,OAAO,cAAc,CAAC,KAAK,KAAK,SAAS,EACzC;QACA,OAAO,cAAc,CAAC,KAAK,CAAC;KAC7B;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CAAC,OAAyB,EAAE,UAAuC;IACzF,MAAM,gBAAgB,GAAG,iCAAiC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAChF,IAAI,CAAC,gBAAgB,EAAE;QACrB,OAAO;KACR;IAED,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC;IAEtD,IAAI,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;QACzC,MAAM,WAAW,GAAG,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAE,CAAC;QAC3D,IAAI,QAAQ,KAAK,UAAU,EAAE;YAC3B,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;SACjC;QAED,IAAI,QAAQ,KAAK,eAAe,EAAE;YAChC,WAAW,CAAC,cAAc,GAAG,mBAAmB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACpE;KACF;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,iCAAiC,CAC/C,OAAyB,EACzB,UAAuC;IAEvC,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE;QAC/C,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY,EAAE;QACxF,MAAM,cAAc,GAAG,2BAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC5E,IAAI,cAAc,EAAE;YAClB,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SAC/D;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAjBD,8EAiBC;AAED,SAAS,MAAM,CACb,OAAyB,EACzB,YAAqB,EACrB,cAAuB,EACvB,cAAqC,EACrC,iBAA0D;IAE1D,IAAI,OAAO,CAAC;IAEZ,IAAI,cAAc,IAAI,YAAY,EAAE;QAClC,OAAO,GAAG,2CAA2C,CAAC;KACvD;SAAM,IAAI,CAAC,cAAc,IAAI,CAAC,YAAY,EAAE;QAC3C,OAAO,GAAG,gDAAgD,CAAC;KAC5D;SAAM,IAAI,cAAc,IAAI,CAAC,YAAY,EAAE;QAC1C,OAAO,GAAG,kEAAkE,CAAC;KAC9E;IAED,IAAI,OAAO,EAAE;QACX,IAAI,iBAAiB,EAAE;YACrB,OAAO,GAAG,4BAAgB,CAAC,OAAO,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;SAC5F;aAAM;YACL,OAAO,GAAG,4BAAgB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SACzC;QAED,OAAO,CAAC,MAAM,CAAC;YACb,OAAO;YACP,IAAI,EAAE,cAAc,CAAC,MAAM;SAC5B,CAAC,CAAC;KACJ;AACH,CAAC"}