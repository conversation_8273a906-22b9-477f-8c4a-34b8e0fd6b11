{"version": 3, "file": "prefer-type-guard.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-type-guard.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,yEAAyF;AACzF,iEAAkE;AAIrD,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,oDAAoD,EAAE,UAAU,IAAiB;gBAC/E,4BAA4B,CAAC,IAA+B,EAAE,OAAO,CAAC,CAAC;YACzE,CAAC;YACD,mBAAmB,CAAC,IAAiB;gBACnC,4BAA4B,CAAC,IAA+B,EAAE,OAAO,CAAC,CAAC;YACzE,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,4BAA4B,CACnC,mBAA4C,EAC5C,OAAyB;IAEzB,IACE,mBAAmB,CAAC,UAAU;QAC9B,mBAAmB,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,KAAK,iBAAiB,EACxE;QACA,OAAO;KACR;IAED,MAAM,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;IACtC,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,CAAC,kBAAkB,EAAE;QACvB,OAAO;KACR;IAED,IAAI,4BAA4B,CAAC,kBAAkB,CAAC,EAAE;QACpD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,kBAAkB,CAAC;QAC3C,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;YACtB,eAAe,CAAC,mBAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SACrD;aAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YAC5B,eAAe,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACtD;KACF;SAAM,IAAI,aAAa,CAAC,kBAAkB,CAAC,EAAE;QAC5C,eAAe,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;KAChF;SAAM,IAAI,UAAU,CAAC,kBAAkB,CAAC,IAAI,UAAU,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACpF,eAAe,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;KACrF;AACH,CAAC;AAED,SAAS,qBAAqB,CAC5B,KAAsC;IAEtC,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACpC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,SAAS,CAAC,IAAI,KAAK,iBAAiB,EAAE;YACxC,OAAO,SAAS,CAAC,QAAQ,CAAC;SAC3B;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,4BAA4B,CACnC,gBAAqC;IAErC,OAAO,CACL,gBAAgB,CAAC,IAAI,KAAK,kBAAkB;QAC5C,CAAC,gBAAgB,CAAC,QAAQ,KAAK,KAAK,IAAI,gBAAgB,CAAC,QAAQ,KAAK,IAAI,CAAC,CAC5E,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,IAA6B,EAC7B,UAA+B,EAC/B,OAAyB;IAEzB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAC3C,MAAM,UAAU,GAAG,gCAAgC,CAAC,UAAU,CAAC,CAAC;IAChE,IAAI,UAAU,IAAK,UAAU,CAAC,CAAC,CAAmB,CAAC,IAAI,KAAK,cAAc,EAAE;QAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACpC,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,EAAE;YACjF,MAAM,oBAAoB,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,OAAO,CAAC,MAAM,CAAC;gBACb,OAAO,EAAE,2DAA2D,oBAAoB,OAAO,cAAc,IAAI;gBACjH,GAAG,EAAE,wCAA4B,CAAC,IAAuB,EAAE,iBAAS,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;aACxF,CAAC,CAAC;SACJ;KACF;AACH,CAAC;AAED,SAAS,gCAAgC,CACvC,IAAyB;IAEzB,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAuB,CAAC;QAC5C,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB,EAAE;YACzE,OAAO,CAAC,MAAM,CAAC,UAAyB,EAAG,MAAM,CAAC,cAAyC,CAAC,CAAC;SAC9F;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,UAAU,CAAC,IAAyB;IAC3C,OAAO,IAAI,CAAC,IAAI,KAAK,iBAAiB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;AACjF,CAAC;AAED,SAAS,WAAW,CAAC,IAAyB;IAC5C,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC;AACjE,CAAC;AAED,SAAS,aAAa,CAAC,IAAyB;IAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC;KACjG;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}