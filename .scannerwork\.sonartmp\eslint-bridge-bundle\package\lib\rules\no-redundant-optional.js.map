{"version": 3, "file": "no-redundant-optional.js", "sourceRoot": "", "sources": ["../../src/rules/no-redundant-optional.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,oCAAsE;AAEzD,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IAED,MAAM,CAAC,OAAyB;QAC9B,IAAI,CAAC,gCAAwB,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;YACrD,OAAO,EAAE,CAAC;SACX;QAED,SAAS,aAAa,CAAC,IAAiB;YACtC,MAAM,MAAM,GAAI,IAEgB,CAAC;YACjC,MAAM,aAAa,GAAG,OAAO;iBAC1B,aAAa,EAAE;iBACf,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,aAAa,EAAE;gBACtC,OAAO;aACR;YAED,MAAM,QAAQ,GAAG,0BAA0B,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YACnE,IAAI,QAAQ,EAAE;gBACZ,MAAM,kBAAkB,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACtC,MAAM,OAAO,GAAG,wBAAgB,CAC9B,gFAAgF,EAChF,kBAAkB,CACnB,CAAC;gBACF,OAAO,CAAC,MAAM,CAAC;oBACb,OAAO;oBACP,GAAG,EAAE,aAAa,CAAC,GAAG;iBACvB,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,oCAAoC,EAAE,CAAC,IAAiB,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;SACjF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,0BAA0B,CAAC,gBAA4C;IAC9E,IAAI,gBAAgB,EAAE;QACpB,OAAO,oBAAoB,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;KAC9D;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,oBAAoB,CAAC,QAA2B;IACvD,IAAI,QAAQ,CAAC,IAAI,KAAK,oBAAoB,EAAE;QAC1C,OAAO,QAAQ,CAAC;KACjB;SAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,aAAa,EAAE;QAC1C,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;KAClF;SAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,qBAAqB,EAAE;QAClD,OAAO,oBAAoB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;KACtD;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}