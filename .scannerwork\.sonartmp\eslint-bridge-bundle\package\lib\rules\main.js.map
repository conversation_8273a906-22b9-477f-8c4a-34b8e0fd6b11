{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../../src/rules/main.ts"], "names": [], "mappings": ";;;AAoBA,mDAAuD;AACvD,uDAA2D;AAC3D,uDAA2D;AAC3D,mFAAqF;AACrF,2DAA+D;AAC/D,2EAA8E;AAC9E,2DAA+D;AAC/D,6DAAgE;AAChE,6DAAgE;AAChE,yEAA6E;AAC7E,6CAAiD;AACjD,uDAA2D;AAC3D,2CAA+C;AAC/C,yEAA0E;AAC1E,mDAAuD;AACvD,uEAA2E;AAC3E,yFAA4F;AAC5F,iFAAmF;AACnF,qDAAyD;AACzD,uEAA0E;AAC1E,6DAAgE;AAChE,uCAA4C;AAC5C,iCAAsC;AACtC,iCAAsC;AACtC,mEAAuE;AACvE,iFAAmF;AACnF,+CAAoD;AACpD,uFAA0F;AAC1F,6EAAgF;AAChF,qEAAwE;AACxE,+EAAkF;AAClF,uDAA2D;AAC3D,+DAAkE;AAClE,6CAAkD;AAClD,qEAAwE;AACxE,mEAAuE;AACvE,+CAAmD;AACnD,+EAAgF;AAChF,yDAA6D;AAC7D,iDAAqD;AACrD,2CAA+C;AAC/C,qCAAyC;AACzC,uEAAyE;AACzE,uDAA2D;AAC3D,iEAAoE;AACpE,mDAAuD;AACvD,iEAAoE;AACpE,mEAAsE;AACtE,uEAA0E;AAC1E,uCAA4C;AAC5C,iDAAqD;AACrD,qEAAuE;AACvE,6EAAgF;AAChF,+FAA+F;AAC/F,uDAA2D;AAC3D,6DAAgE;AAChE,qDAAyD;AACzD,qDAAwD;AACxD,qEAAwE;AACxE,+DAAkE;AAClE,+DAAkE;AAClE,6EAA+E;AAC/E,iEAAoE;AACpE,uDAA0D;AAC1D,mEAAsE;AACtE,iEAAmE;AACnE,uEAAwE;AACxE,uEAAyE;AACzE,2DAA8D;AAC9D,mDAAsD;AACtD,mDAAsD;AACtD,2EAA6E;AAC7E,+DAAkE;AAClE,iFAAkF;AAClF,6DAA+D;AAC/D,yFAA0F;AAC1F,qDAAwD;AACxD,iEAAoE;AACpE,2EAA8E;AAC9E,yEAA4E;AAC5E,uDAA0D;AAC1D,2DAA8D;AAC9D,yEAA4E;AAC5E,6DAAgE;AAChE,iDAAoD;AACpD,uEAA0E;AAC1E,6EAA+E;AAC/E,yDAA4D;AAC5D,yEAA4E;AAC5E,yDAA4D;AAC5D,iEAAoE;AACpE,mDAAsD;AACtD,2CAA+C;AAC/C,mDAAsD;AACtD,+EAAiF;AACjF,yDAA4D;AAC5D,iEAAoE;AACpE,mEAAsE;AACtE,yDAA4D;AAC5D,yDAA4D;AAC5D,+EAAiF;AACjF,mDAAsD;AACtD,2EAA8E;AAC9E,mEAAsE;AACtE,yEAA4E;AAC5E,mEAAsE;AACtE,yEAA4E;AAC5E,6DAAgE;AAChE,6DAAgE;AAChE,iEAAmE;AACnE,6DAA+D;AAC/D,qCAAyC;AACzC,qDAAwD;AACxD,mEAAsE;AACtE,uEAA0E;AAC1E,mFAAqF;AACrF,uDAA0D;AAC1D,2DAA8D;AAC9D,+EAAiF;AACjF,iEAAoE;AACpE,uEAA0E;AAC1E,iGAAkG;AAClG,qDAAwD;AACxD,iDAAoD;AACpD,6DAAgE;AAChE,mEAAsE;AACtE,+FAAgG;AAChG,yDAA6D;AAC7D,uEAA0E;AAC1E,6CAAiD;AACjD,iDAAqD;AACrD,+DAAkE;AAClE,yEAA4E;AAC5E,2DAA8D;AAC9D,iDAAqD;AACrD,yDAA6D;AAC7D,mDAAuD;AACvD,mFAAuF;AACvF,iDAAqD;AACrD,iEAAqE;AACrE,+EAAkF;AAClF,uCAA4C;AAC5C,qEAAuE;AACvE,uDAA0D;AAC1D,iFAAkF;AAClF,iEAAoE;AACpE,iEAAmE;AACnE,+CAAmD;AACnD,qDAAyD;AACzD,2EAA8E;AAC9E,6DAAiE;AACjE,yDAA6D;AAC7D,qEAAwE;AACxE,yCAA6C;AAC7C,6FAA4F;AAC5F,mDAAuD;AACvD,qEAAyE;AACzE,+DAAmE;AACnE,2DAA8D;AAC9D,iEAAoE;AACpE,qDAAwD;AACxD,yEAA4E;AAC5E,2FAA4F;AAC5F,mDAAuD;AACvD,yCAA6C;AAC7C,yCAA6C;AAC7C,yDAA4D;AAC5D,iDAAoD;AACpD,qDAAwD;AACxD,mCAAwC;AAExC,MAAM,WAAW,GAAuC,EAAE,CAAC;AA6KnC,4BAAK;AA3K7B,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAY,CAAC;AAC5C,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAc,CAAC;AAChD,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAc,CAAC;AAChD,WAAW,CAAC,+BAA+B,CAAC,GAAG,oCAA0B,CAAC;AAC1E,WAAW,CAAC,mBAAmB,CAAC,GAAG,wBAAgB,CAAC;AACpD,WAAW,CAAC,2BAA2B,CAAC,GAAG,gCAAuB,CAAC;AACnE,WAAW,CAAC,mBAAmB,CAAC,GAAG,wBAAgB,CAAC;AACpD,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAgB,CAAC;AACrD,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAgB,CAAC;AACrD,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAuB,CAAC;AAClE,WAAW,CAAC,YAAY,CAAC,GAAG,iBAAS,CAAC;AACtC,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAc,CAAC;AAChD,WAAW,CAAC,WAAW,CAAC,GAAG,gBAAQ,CAAC;AACpC,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAoB,CAAC;AAC/D,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAY,CAAC;AAC5C,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAsB,CAAC;AAChE,WAAW,CAAC,kCAAkC,CAAC,GAAG,uCAA8B,CAAC;AACjF,WAAW,CAAC,8BAA8B,CAAC,GAAG,mCAAyB,CAAC;AACxE,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAa,CAAC;AAC9C,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAqB,CAAC;AAC/D,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAgB,CAAC;AACrD,WAAW,CAAC,SAAS,CAAC,GAAG,cAAO,CAAC;AACjC,WAAW,CAAC,MAAM,CAAC,GAAG,WAAI,CAAC;AAC3B,WAAW,CAAC,MAAM,CAAC,GAAG,WAAI,CAAC;AAC3B,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAoB,CAAC;AAC5D,WAAW,CAAC,8BAA8B,CAAC,GAAG,mCAAyB,CAAC;AACxE,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAW,CAAC;AACzC,WAAW,CAAC,iCAAiC,CAAC,GAAG,sCAA6B,CAAC;AAC/E,WAAW,CAAC,4BAA4B,CAAC,GAAG,iCAAwB,CAAC;AACrE,WAAW,CAAC,wBAAwB,CAAC,GAAG,6BAAoB,CAAC;AAC7D,WAAW,CAAC,6BAA6B,CAAC,GAAG,kCAAyB,CAAC;AACvE,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAc,CAAC;AAChD,WAAW,CAAC,qBAAqB,CAAC,GAAG,0BAAiB,CAAC;AACvD,WAAW,CAAC,YAAY,CAAC,GAAG,iBAAU,CAAC;AACvC,WAAW,CAAC,wBAAwB,CAAC,GAAG,6BAAoB,CAAC;AAC7D,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAoB,CAAC;AAC5D,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAU,CAAC;AACxC,WAAW,CAAC,6BAA6B,CAAC,GAAG,kCAAuB,CAAC;AACrE,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAe,CAAC;AAClD,WAAW,CAAC,cAAc,CAAC,GAAG,mBAAW,CAAC;AAC1C,WAAW,CAAC,WAAW,CAAC,GAAG,gBAAQ,CAAC;AACpC,WAAW,CAAC,QAAQ,CAAC,GAAG,aAAK,CAAC;AAC9B,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAoB,CAAC;AAC9D,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAc,CAAC;AAChD,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAY,CAAC;AAC5C,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAmB,CAAC;AAC3D,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAqB,CAAC;AAC/D,WAAW,CAAC,SAAS,CAAC,GAAG,cAAO,CAAC;AACjC,WAAW,CAAC,cAAc,CAAC,GAAG,mBAAW,CAAC;AAC1C,WAAW,CAAC,wBAAwB,CAAC,GAAG,6BAAmB,CAAC;AAC5D,WAAW,CAAC,4BAA4B,CAAC,GAAG,iCAAwB,CAAC;AACrE,WAAW,CAAC,qCAAqC,CAAC,GAAG,0CAA8B,CAAC;AACpF,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAc,CAAC;AAChD,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAgB,CAAC;AACrD,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAa,CAAC;AAC9C,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAY,CAAC;AAC7C,WAAW,CAAC,wBAAwB,CAAC,GAAG,6BAAoB,CAAC;AAC7D,WAAW,CAAC,qBAAqB,CAAC,GAAG,0BAAiB,CAAC;AACvD,WAAW,CAAC,qBAAqB,CAAC,GAAG,0BAAiB,CAAC;AACvD,WAAW,CAAC,4BAA4B,CAAC,GAAG,iCAAuB,CAAC;AACpE,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAa,CAAC;AAC/C,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAmB,CAAC;AAC3D,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAiB,CAAC;AACxD,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAmB,CAAC;AAC7D,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAoB,CAAC;AAC9D,WAAW,CAAC,mBAAmB,CAAC,GAAG,wBAAe,CAAC;AACnD,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAW,CAAC;AAC3C,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAW,CAAC;AAC3C,WAAW,CAAC,2BAA2B,CAAC,GAAG,gCAAsB,CAAC;AAClE,WAAW,CAAC,qBAAqB,CAAC,GAAG,0BAAiB,CAAC;AACvD,WAAW,CAAC,8BAA8B,CAAC,GAAG,mCAAwB,CAAC;AACvE,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAe,CAAC;AACpD,WAAW,CAAC,kCAAkC,CAAC,GAAG,uCAA4B,CAAC;AAC/E,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAY,CAAC;AAC7C,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,2BAA2B,CAAC,GAAG,gCAAuB,CAAC;AACnE,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAsB,CAAC;AACjE,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAa,CAAC;AAC/C,WAAW,CAAC,mBAAmB,CAAC,GAAG,wBAAe,CAAC;AACnD,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAsB,CAAC;AACjE,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAgB,CAAC;AACrD,WAAW,CAAC,cAAc,CAAC,GAAG,mBAAU,CAAC;AACzC,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAqB,CAAC;AAC/D,WAAW,CAAC,4BAA4B,CAAC,GAAG,iCAAuB,CAAC;AACpE,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAc,CAAC;AACjD,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAsB,CAAC;AACjE,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAc,CAAC;AACjD,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAW,CAAC;AAC3C,WAAW,CAAC,WAAW,CAAC,GAAG,gBAAQ,CAAC;AACpC,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAW,CAAC;AAC3C,WAAW,CAAC,6BAA6B,CAAC,GAAG,kCAAwB,CAAC;AACtE,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAc,CAAC;AACjD,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAmB,CAAC;AAC3D,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAc,CAAC;AACjD,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAc,CAAC;AACjD,WAAW,CAAC,6BAA6B,CAAC,GAAG,kCAAwB,CAAC;AACtE,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAW,CAAC;AAC3C,WAAW,CAAC,2BAA2B,CAAC,GAAG,gCAAuB,CAAC;AACnE,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAmB,CAAC;AAC3D,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAsB,CAAC;AACjE,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAmB,CAAC;AAC3D,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAsB,CAAC;AACjE,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAgB,CAAC;AACrD,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAgB,CAAC;AACrD,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAiB,CAAC;AACxD,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAe,CAAC;AACpD,WAAW,CAAC,QAAQ,CAAC,GAAG,aAAK,CAAC;AAC9B,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAY,CAAC;AAC7C,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAmB,CAAC;AAC3D,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAqB,CAAC;AAC/D,WAAW,CAAC,+BAA+B,CAAC,GAAG,oCAA0B,CAAC;AAC1E,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAa,CAAC;AAC/C,WAAW,CAAC,mBAAmB,CAAC,GAAG,wBAAe,CAAC;AACnD,WAAW,CAAC,6BAA6B,CAAC,GAAG,kCAAwB,CAAC;AACtE,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAqB,CAAC;AAC/D,WAAW,CAAC,sCAAsC,CAAC,GAAG,2CAAgC,CAAC;AACvF,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAY,CAAC;AAC7C,WAAW,CAAC,cAAc,CAAC,GAAG,mBAAU,CAAC;AACzC,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAgB,CAAC;AACrD,WAAW,CAAC,uBAAuB,CAAC,GAAG,4BAAmB,CAAC;AAC3D,WAAW,CAAC,qCAAqC,CAAC,GAAG,0CAA+B,CAAC;AACrF,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAe,CAAC;AAClD,WAAW,CAAC,yBAAyB,CAAC,GAAG,8BAAqB,CAAC;AAC/D,WAAW,CAAC,YAAY,CAAC,GAAG,iBAAS,CAAC;AACtC,WAAW,CAAC,cAAc,CAAC,GAAG,mBAAW,CAAC;AAC1C,WAAW,CAAC,qBAAqB,CAAC,GAAG,0BAAiB,CAAC;AACvD,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAsB,CAAC;AACjE,WAAW,CAAC,mBAAmB,CAAC,GAAG,wBAAe,CAAC;AACnD,WAAW,CAAC,cAAc,CAAC,GAAG,mBAAW,CAAC;AAC1C,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAe,CAAC;AAClD,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAY,CAAC;AAC5C,WAAW,CAAC,+BAA+B,CAAC,GAAG,oCAA4B,CAAC;AAC5E,WAAW,CAAC,cAAc,CAAC,GAAG,mBAAW,CAAC;AAC1C,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAmB,CAAC;AAC1D,WAAW,CAAC,6BAA6B,CAAC,GAAG,kCAAyB,CAAC;AACvE,WAAW,CAAC,SAAS,CAAC,GAAG,cAAO,CAAC;AACjC,WAAW,CAAC,wBAAwB,CAAC,GAAG,6BAAmB,CAAC;AAC5D,WAAW,CAAC,iBAAiB,CAAC,GAAG,sBAAa,CAAC;AAC/C,WAAW,CAAC,8BAA8B,CAAC,GAAG,mCAAwB,CAAC;AACvE,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAiB,CAAC;AACxD,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAU,CAAC;AACxC,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAa,CAAC;AAC9C,WAAW,CAAC,2BAA2B,CAAC,GAAG,gCAAuB,CAAC;AACnE,WAAW,CAAC,oBAAoB,CAAC,GAAG,yBAAiB,CAAC;AACtD,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAe,CAAC;AAClD,WAAW,CAAC,wBAAwB,CAAC,GAAG,6BAAoB,CAAC;AAC7D,WAAW,CAAC,UAAU,CAAC,GAAG,eAAO,CAAC;AAClC,WAAW,CAAC,oCAAoC,CAAC,GAAG,yCAA4B,CAAC;AACjF,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAY,CAAC;AAC5C,WAAW,CAAC,wBAAwB,CAAC,GAAG,6BAAqB,CAAC;AAC9D,WAAW,CAAC,qBAAqB,CAAC,GAAG,0BAAkB,CAAC;AACxD,WAAW,CAAC,mBAAmB,CAAC,GAAG,wBAAe,CAAC;AACnD,WAAW,CAAC,sBAAsB,CAAC,GAAG,2BAAkB,CAAC;AACzD,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAY,CAAC;AAC7C,WAAW,CAAC,0BAA0B,CAAC,GAAG,+BAAsB,CAAC;AACjE,WAAW,CAAC,mCAAmC,CAAC,GAAG,wCAA6B,CAAC;AACjF,WAAW,CAAC,eAAe,CAAC,GAAG,oBAAY,CAAC;AAC5C,WAAW,CAAC,UAAU,CAAC,GAAG,eAAO,CAAC;AAClC,WAAW,CAAC,UAAU,CAAC,GAAG,eAAO,CAAC;AAClC,WAAW,CAAC,kBAAkB,CAAC,GAAG,uBAAc,CAAC;AACjD,WAAW,CAAC,cAAc,CAAC,GAAG,mBAAU,CAAC;AACzC,WAAW,CAAC,gBAAgB,CAAC,GAAG,qBAAY,CAAC;AAC7C,WAAW,CAAC,OAAO,CAAC,GAAG,YAAK,CAAC"}