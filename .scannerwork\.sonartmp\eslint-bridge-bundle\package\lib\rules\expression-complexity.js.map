{"version": 3, "file": "expression-complexity.js", "sourceRoot": "", "sources": ["../../src/rules/expression-complexity.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,oCAA4C;AAE/B,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,SAAS,EAAE;YACnB;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9B,MAAM,cAAc,GAA2B,CAAC,IAAI,oBAAoB,EAAE,CAAC,CAAC;QAC5E,OAAO;YACL,GAAG,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACzB,MAAM,IAAI,GAAG,IAAqB,CAAC;gBACnC,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;oBAC3B,MAAM,IAAI,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACvD,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBAChC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;iBACnD;qBAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;oBAC5B,cAAc,CAAC,IAAI,CAAC,IAAI,oBAAoB,EAAE,CAAC,CAAC;iBACjD;YACH,CAAC;YACD,QAAQ,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC9B,MAAM,IAAI,GAAG,IAAqB,CAAC;gBACnC,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;oBAC3B,MAAM,IAAI,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACvD,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;wBAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;wBAChD,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE;4BAC1B,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;yBAC5C;wBACD,IAAI,CAAC,kCAAkC,EAAE,CAAC;qBAC3C;iBACF;qBAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;oBAC5B,cAAc,CAAC,GAAG,EAAE,CAAC;iBACtB;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,MAAM,oBAAoB;IAA1B;QACE,gBAAW,GAAG,CAAC,CAAC;QAChB,cAAS,GAAgB,EAAE,CAAC;IAyB9B,CAAC;IAvBC,WAAW,CAAC,QAAmB;QAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED,wBAAwB;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,wBAAwB;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,kBAAkB;QAChB,OAAO,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,sBAAsB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,kCAAkC;QAChC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACtB,CAAC;CACF;AAED,SAAS,WAAW,CAAC,IAAmB;IACtC,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,oBAAoB;QAClC,CAAC,IAAI,CAAC,IAAI,KAAK,qBAAqB,IAAI,IAAI,CAAC,SAAS,CAAC;QACvD,IAAI,CAAC,IAAI,KAAK,kBAAkB;QAChC,IAAI,CAAC,IAAI,KAAK,gBAAgB;QAC9B,IAAI,CAAC,IAAI,KAAK,YAAY,CAC3B,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAmB;IAC5C,OAAO,IAAI,CAAC,IAAI,KAAK,uBAAuB,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC;AACpF,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAmB,EAAE,OAAyB;IACtE,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAC3C,IAAI,IAAI,CAAC,IAAI,KAAK,uBAAuB,EAAE;QACzC,OAAO,UAAU,CAAC,aAAa,CAC7B,IAAI,CAAC,IAAmB,EACxB,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,CAC3D,CAAC;KACJ;SAAM;QACL,MAAM,IAAI,GAAG,IAAgC,CAAC;QAC9C,OAAO,UAAU,CAAC,aAAa,CAC7B,IAAI,CAAC,IAAI,EACT,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CACrE,CAAC;KACJ;AACH,CAAC;AAED,SAAS,WAAW,CAClB,IAAmB,EACnB,SAAsB,EACtB,GAAW,EACX,OAAyB;IAEzB,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC;IACpC,MAAM,OAAO,GAAG,+CAA+C,UAAU,6CAA6C,GAAG,IAAI,CAAC;IAC9H,MAAM,wBAAwB,GAAG,SAAS,CAAC;IAC3C,MAAM,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD,MAAM,IAAI,GAAG,UAAU,GAAG,GAAG,CAAC;IAC9B,OAAO,CAAC,MAAM,CAAC;QACb,IAAI,EAAE,IAAmB;QACzB,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,wBAAwB,EAAE,iBAAiB,EAAE,IAAI,CAAC;KACtF,CAAC,CAAC;AACL,CAAC"}