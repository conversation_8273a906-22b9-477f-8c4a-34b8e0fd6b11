{"version": 3, "file": "no-invalid-await.js", "sourceRoot": "", "sources": ["../../src/rules/no-invalid-await.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAIjD,+CAAiC;AACjC,oCAAyE;AAE5D,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACtC,OAAO;gBACL,eAAe,EAAE,CAAC,IAAiB,EAAE,EAAE;oBACrC,MAAM,WAAW,GAAG,2BAAmB,CACpC,IAA+B,CAAC,QAAQ,EACzC,QAAQ,CACT,CAAC;oBACF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;wBAC/E,OAAO,CAAC,MAAM,CAAC;4BACb,OAAO,EAAE,mDAAmD;4BAC5D,IAAI;yBACL,CAAC,CAAC;qBACJ;gBACH,CAAC;aACF,CAAC;SACH;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAC;AAEF,SAAS,aAAa,CAAC,IAAa;IAClC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC9C,OAAO,OAAO,CAAC,YAAY,IAAI,YAAY,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC7E,CAAC;AAED,SAAS,KAAK,CAAC,IAAa;IAC1B,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,OAAO,CAAC,IAAa;IAC5B,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAClD,CAAC"}