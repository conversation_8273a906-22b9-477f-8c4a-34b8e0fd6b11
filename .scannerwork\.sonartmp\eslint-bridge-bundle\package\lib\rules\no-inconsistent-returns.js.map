{"version": 3, "file": "no-inconsistent-returns.js", "sourceRoot": "", "sources": ["../../src/rules/no-inconsistent-returns.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,iEAAkE;AAClE,yEAAyF;AACzF,oCAA4C;AAgB/B,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IAED,MAAM,CAAC,OAAyB;QAC9B,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,oBAAoB,GAAsB,EAAE,CAAC;QACnD,MAAM,mBAAmB,GAAG,CAAC,IAAiB,EAAE,EAAE,CAChD,4BAA4B,CAC1B,IAA+B,EAC/B,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CACtD,CAAC;QAEJ,SAAS,4BAA4B,CACnC,IAA6B,EAC7B,eAAiC;YAEjC,IACE,CAAC,eAAe;gBAChB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU;oBAChB,0CAA0C,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,EAC7E;gBACA,OAAO;aACR;YAED,8BAA8B,CAAC,eAAe,CAAC,CAAC;YAEhD,IAAI,sBAAsB,CAAC,eAAe,CAAC,EAAE;gBAC3C,MAAM,CAAC,wBAAwB,EAAE,yBAAyB,CAAC,GAAG,qBAAqB,CACjF,eAAe,EACf,IAAmB,CACpB,CAAC;gBACF,MAAM,OAAO,GAAG,wBAAgB,CAC9B,sDAAsD,EACtD,wBAAwB,EACxB,yBAAyB,CAC1B,CAAC;gBAEF,OAAO,CAAC,MAAM,CAAC;oBACb,OAAO;oBACP,GAAG,EAAE,wCAA4B,CAAC,IAAuB,EAAE,iBAAS,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;iBACxF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,8BAA8B,CAAC,eAAgC;YACtE,mFAAmF;YACnF,sFAAsF;YACtF,uDAAuD;YACvD,eAAe,CAAC,sBAAsB,GAAG,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CACpF,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAC7B,CAAC;QACJ,CAAC;QAED,SAAS,qBAAqB,CAC5B,eAAgC,EAChC,IAAiB;YAEjB,MAAM,wBAAwB,GAAG,eAAe,CAAC,gBAAgB,CAAC,KAAK,EAAqB,CAAC;YAC7F,MAAM,yBAAyB,GAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAC9E,eAAe,CAAC,EAAE,CAChB,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,sBAAsB,CAC1E,CAAC;YAEF,IAAI,eAAe,CAAC,sBAAsB,EAAE;gBAC1C,MAAM,oBAAoB,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;gBACzF,IAAI,CAAC,CAAC,oBAAoB,EAAE;oBAC1B,wBAAwB,CAAC,IAAI,CAAC,oBAAqC,CAAC,CAAC;oBACrE,yBAAyB,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;iBACjE;aACF;YAED,OAAO,CAAC,wBAAwB,EAAE,yBAAyB,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO;YACL,eAAe,CAAC,QAAuB;gBACrC,oBAAoB,CAAC,IAAI,CAAC;oBACxB,QAAQ;oBACR,uBAAuB,EAAE,KAAK;oBAC9B,0BAA0B,EAAE,KAAK;oBACjC,sBAAsB,EAAE,KAAK;oBAC7B,gBAAgB,EAAE,EAAE;iBACrB,CAAC,CAAC;YACL,CAAC;YACD,aAAa;gBACX,oBAAoB,CAAC,GAAG,EAAE,CAAC;YAC7B,CAAC;YAED,eAAe,CAAC,IAAiB;gBAC/B,MAAM,cAAc,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7E,IAAI,CAAC,CAAC,cAAc,EAAE;oBACpB,MAAM,eAAe,GAAG,IAA8B,CAAC;oBACvD,cAAc,CAAC,uBAAuB;wBACpC,cAAc,CAAC,uBAAuB,IAAI,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC;oBACvE,cAAc,CAAC,0BAA0B;wBACvC,cAAc,CAAC,0BAA0B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;oBACzE,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;iBACvD;YACH,CAAC;YACD,0BAA0B,EAAE,mBAAmB;YAC/C,yBAAyB,EAAE,mBAAmB;YAC9C,8BAA8B,EAAE,mBAAmB;SACpD,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,sBAAsB,CAAC,eAAgC;IAC9D,OAAO,CACL,eAAe,CAAC,uBAAuB;QACvC,CAAC,eAAe,CAAC,0BAA0B,IAAI,eAAe,CAAC,sBAAsB,CAAC,CACvF,CAAC;AACJ,CAAC;AAED,SAAS,0CAA0C,CAAC,cAAiC;IACnF,OAAO,CACL,UAAU,CAAC,cAAc,CAAC;QAC1B,CAAC,cAAc,CAAC,IAAI,KAAK,aAAa;YACpC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACxE,CAAC,cAAc,CAAC,IAAI,KAAK,qBAAqB;YAC5C,0CAA0C,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAC7E,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,QAA2B;IAC7C,OAAO,CACL,QAAQ,CAAC,IAAI,KAAK,oBAAoB;QACtC,QAAQ,CAAC,IAAI,KAAK,eAAe;QACjC,QAAQ,CAAC,IAAI,KAAK,gBAAgB,CACnC,CAAC;AACJ,CAAC"}