{"version": 3, "file": "frame-ancestors.js", "sourceRoot": "", "sources": ["../../src/rules/frame-ancestors.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAA4E;AAC5E,mDAA0C;AAE1C,MAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,MAAM,UAAU,GAAG,YAAY,CAAC;AAChC,MAAM,UAAU,GAAG,YAAY,CAAC;AAChC,MAAM,IAAI,GAAG,QAAQ,CAAC;AACtB,MAAM,uBAAuB,GAAG,uBAAuB,CAAC;AACxD,MAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAC/C,MAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAEpC,QAAA,IAAI,GAAoB,uBAAO,CAAC,+BAA+B,CAC1E,2DAA2D,EAC3D,qFAAqF,CACtF,CAAC;AAEF,SAAS,2DAA2D,CAClE,OAAyB,EACzB,IAA2B;IAE3B,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IACjC,IAAI,uBAAuB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAC/D,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;QACvB,MAAM,eAAe,GAAG,mCAA2B,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACzE,IAAI,eAAe,EAAE;YACnB,MAAM,mBAAmB,GAAG,yBAAyB,CAAC,eAAe,CAAC,CAAC;YACvE,IAAI,CAAC,mBAAmB,EAAE;gBACxB,OAAO,CAAC,eAAe,CAAC,CAAC;aAC1B;YACD,IAAI,+BAA+B,CAAC,mBAAmB,CAAC,EAAE;gBACxD,OAAO,CAAC,mBAAmB,CAAC,CAAC;aAC9B;SACF;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,uBAAuB,CAAC,OAAyB,EAAE,QAA+B;;IACzF,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IAE5B,kBAAkB;IAClB,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,IAAI,CAAA,MAAA,2BAAmB,CAAC,OAAO,EAAE,MAAM,CAAC,0CAAE,KAAK,MAAK,UAAU,EAAE;QAC9F,OAAO,IAAI,CAAC;KACb;IAED,2CAA2C;IAC3C,IACE,MAAM,CAAC,IAAI,KAAK,kBAAkB;QAClC,CAAA,MAAA,2BAAmB,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,0CAAE,KAAK,MAAK,MAAM;QAC7D,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;QACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,uBAAuB,EAChD;QACA,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,+BAA+B,CAAC,cAA+B;IACtE,MAAM,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC;IACjC,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,iBAAiB;QAChC,OAAO,CACL,KAAK,CAAC,QAAQ,CAAC,IAAI,CACjB,CAAC,CAAC,EAAE,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,MAAK,SAAS,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,CAC9E,CACF,CACF,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAAC,UAA2B;IAC5D,MAAM,YAAY,GAAG,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,CAAC;IACrE,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;QACtC,MAAM,aAAa,GAAG,mCAA2B,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACjF,IAAI,aAAa,EAAE;YACjB,OAAO,aAAa,CAAC;SACtB;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}