{"version": 3, "file": "non-number-in-arithmetic-expression.js", "sourceRoot": "", "sources": ["../../src/rules/non-number-in-arithmetic-expression.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;AAIjD,4DAA4B;AAC5B,oCAKkB;AAElB,MAAM,OAAO,GAAG,qCAAqC,CAAC;AACzC,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QAED,OAAO;YACL,gBAAgB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACtC,MAAM,gBAAgB,GAAG,IAA+B,CAAC;gBACzD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC;gBAC3C,MAAM,QAAQ,GAAG,2BAAmB,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACtE,MAAM,SAAS,GAAG,2BAAmB,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACxE,IAAI,QAAQ,KAAK,GAAG,EAAE;oBACpB,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;iBAC3D;gBACD,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE;oBAClF,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;iBACjE;gBACD,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,EAAE;oBAChF,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;iBACjE;YACH,CAAC;YACD,oBAAoB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC1C,MAAM,oBAAoB,GAAG,IAAmC,CAAC;gBACjE,MAAM,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;gBAC/C,MAAM,QAAQ,GAAG,2BAAmB,CAAC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAC1E,MAAM,SAAS,GAAG,2BAAmB,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAC5E,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACrB,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC;iBAC/D;gBACD,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACpF,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC;iBACrE;YACH,CAAC;YACD,+BAA+B,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACrD,MAAM,eAAe,GAAG,IAA8B,CAAC;gBACvD,MAAM,IAAI,GAAG,2BAAmB,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACrE,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;oBAC/B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,eAAe,CAAC,QAAQ;wBAC9B,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;qBACvC,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,gBAAgB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACtC,MAAM,gBAAgB,GAAG,IAA+B,CAAC;gBACzD,MAAM,IAAI,GAAG,2BAAmB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACtE,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;oBAC/B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,gBAAgB,CAAC,QAAQ;wBAC/B,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;qBACvC,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,wBAAwB,CAC/B,QAAiB,EACjB,SAAkB,EAClB,QAA2D;;IAE3D,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,IAAI,EAAE;QACzC,OAAO,KAAK,CAAC;KACd;IACD,IACE,CAAA,MAAA,QAAQ,CAAC,MAAM,0CAAE,IAAI,MAAK,MAAM;QAChC,CAAC,CAAA,MAAA,SAAS,CAAC,MAAM,0CAAE,IAAI,MAAK,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,oBAAE,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAC/E;QACA,OAAO,IAAI,CAAC;KACb;IACD,IAAI,CAAA,MAAA,SAAS,CAAC,MAAM,0CAAE,IAAI,MAAK,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,oBAAE,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QAChF,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,SAAS,CAChB,QAAiB,EACjB,SAAkB,EAClB,UAAiE,EACjE,OAAyB;IAEzB,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;QACpD,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,UAAU,CAAC,KAAK;YACtB,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACtD,CAAC,CAAC;KACJ;IACD,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;QACpD,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACvD,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,eAAe,CACtB,QAAiB,EACjB,SAAkB,EAClB,UAAiE,EACjE,OAAyB;IAEzB,IAAI,iBAAiB,CAAC,QAAQ,CAAC,IAAI,qBAAqB,CAAC,SAAS,CAAC,EAAE;QACnE,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,UAAU,CAAC,KAAK;YACtB,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;SACvC,CAAC,CAAC;KACJ;SAAM,IAAI,iBAAiB,CAAC,SAAS,CAAC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,EAAE;QAC1E,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;SACvC,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,eAAe,CACtB,QAAiB,EACjB,SAAkB,EAClB,UAAiE,EACjE,OAAyB;IAEzB,IAAI,wBAAwB,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,CAAC,EAAE;QACtE,OAAO;KACR;IACD,MAAM,kBAAkB,GAAG,EAAE,CAAC;IAC9B,IAAI,qBAAqB,CAAC,QAAQ,CAAC,EAAE;QACnC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KAC1C;IACD,IAAI,qBAAqB,CAAC,SAAS,CAAC,EAAE;QACpC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC3C;IACD,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;QACnC,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,wBAAgB,CACvB,sDAAsD,EACtD,kBAAkB,CACnB;SACF,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,eAAe,CAAC,IAAa;;IACpC,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,IAAI,CAAC;KACb;IACD,OAAO,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,MAAM,CAAC;AACtC,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAa;IACtC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,SAAS,CAAC,IAAa;;IAC9B,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,oBAAE,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,SAAS,CAAC;AACxF,CAAC;AAED,SAAS,QAAQ,CAAC,IAAa;;IAC7B,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,oBAAE,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,QAAQ,CAAC;AACtF,CAAC;AAED,SAAS,qBAAqB,CAAC,IAAa;;IAC1C,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,oBAAY,CAAC,IAAI,CAAC,IAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,MAAM,CAAC;AAC/E,CAAC"}