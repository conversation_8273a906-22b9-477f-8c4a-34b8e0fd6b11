{"version": 3, "file": "publicly-writable-directories.js", "sourceRoot": "", "sources": ["../../src/rules/publicly-writable-directories.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,MAAM,gBAAgB,GAAG;IACvB,OAAO;IACP,WAAW;IACX,WAAW;IACX,WAAW;IACX,cAAc;IACd,YAAY;IACZ,gBAAgB;IAChB,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,mBAAmB;CACpB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;AAErC,MAAM,2BAA2B,GAAG,IAAI,MAAM,CAC5C,iEAAiE,EACjE,GAAG,CACJ,CAAC;AACF,MAAM,uBAAuB,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAErE,MAAM,OAAO,GAAG,+DAA+D,CAAC;AACnE,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,OAAO,EAAE,CAAC,IAAiB,EAAE,EAAE;;gBAC7B,MAAM,OAAO,GAAG,IAAsB,CAAC;gBACvC,+EAA+E;gBAC/E,MAAM,KAAK,GAAG,MAAA,OAAO,CAAC,GAAG,0CAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC5D,IACE,KAAK;oBACL,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC,EACxF;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,OAAO;wBACb,OAAO,EAAE,OAAO;qBACjB,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,gBAAgB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACtC,MAAM,gBAAgB,GAAG,IAA+B,CAAC;gBACzD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC;gBAC9C,IACE,QAAQ,CAAC,IAAI,KAAK,YAAY;oBAC9B,CAAC,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAChD,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAClC;oBACA,OAAO;iBACR;gBACD,IACE,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;oBACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK;oBAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY;oBACnC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,EAChC;oBACA,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;iBAC9D;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}