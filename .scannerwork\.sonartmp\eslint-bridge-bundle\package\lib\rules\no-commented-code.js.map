{"version": 3, "file": "no-commented-code.js", "sourceRoot": "", "sources": ["../../src/rules/no-commented-code.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,gDAAgD;;;;;;;;;;;;;;;;;;;;;;AAEhD,mCAA0C;AAG1C,sCAAqE;AACrE,4DAA8C;AAE9C,MAAM,mBAAmB,GAAG,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;AAO3E,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,SAAS,kBAAkB,CAAC,QAA4B;YACtD,MAAM,eAAe,GAAmB,EAAE,CAAC;YAC3C,IAAI,YAAY,GAAuB,EAAE,CAAC;YAC1C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC9B,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE;oBAC5B,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;iBAClE;qBAAM,IACL,YAAY,CAAC,MAAM,KAAK,CAAC;oBACzB,uBAAuB,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EACvE;oBACA,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC5B;qBAAM;oBACL,eAAe,CAAC,IAAI,CAAC;wBACnB,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;wBACpE,KAAK,EAAE,YAAY;qBACpB,CAAC,CAAC;oBACH,YAAY,GAAG,CAAC,OAAO,CAAC,CAAC;iBAC1B;aACF;YAED,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3B,eAAe,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;oBACpE,KAAK,EAAE,YAAY;iBACpB,CAAC,CAAC;aACJ;YAED,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,SAAS,uBAAuB,CAAC,QAA0B,EAAE,IAAsB;YACjF,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;YAC5C,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,KAAK,eAAe,EAAE;gBACnD,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACtE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,eAAe,CAAC;aACzE;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,cAAc,EAAE,GAAG,EAAE;gBACnB,MAAM,eAAe,GAAG,kBAAkB,CACxC,OAAO,CAAC,aAAa,EAAE,CAAC,cAAc,EAAwB,CAC/D,CAAC;gBACF,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;oBACrC,MAAM,cAAc,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBACjD,IAAI,cAAc,KAAK,GAAG,IAAI,YAAY,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,EAAE;wBAC/E,OAAO,CAAC,MAAM,CAAC;4BACb,OAAO,EAAE,iCAAiC;4BAC1C,GAAG,EAAE,kBAAkB,CAAC,YAAY,CAAC,KAAK,CAAC;yBAC5C,CAAC,CAAC;qBACJ;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,qBAAqB,CAAC,SAAsB,EAAE,IAAgB;IACrE,IAAI,SAAS,CAAC,IAAI,KAAK,qBAAqB,EAAE;QAC5C,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,IACE,UAAU,CAAC,IAAI,KAAK,YAAY;YAChC,UAAU,CAAC,IAAI,KAAK,oBAAoB;YACxC,kBAAkB,CAAC,UAAU,CAAC;YAC9B,iBAAiB,CAAC,UAAU,CAAC;YAC7B,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,EAC3D;YACA,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,WAAW,CAAC,UAA8B,EAAE,IAAgB;IACnE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,MAAM,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CACL,mBAAmB,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC;YAClD,sBAAsB,CAAC,eAAe,CAAC;YACvC,qBAAqB,CAAC,eAAe,EAAE,IAAI,CAAC,CAC7C,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,WAAW,GAAG,cAAK,CAAC,KAAK,CAAC,KAAK,EAAE,oBAAW,CAAC,6BAAoB,CAAC,EAAE,KAAK,CAAC,CAAC;IACjF,OAAO,CACL,WAAW,YAAY,mBAAU;QACjC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;QAC/B,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAChD,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAa;IACxC,MAAM,iBAAiB,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;IAC3D,MAAM,kBAAkB,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;IAC5D,MAAM,aAAa,GAAG,iBAAiB,GAAG,kBAAkB,CAAC;IAC7D,IAAI,aAAa,GAAG,CAAC,EAAE;QACrB,OAAO,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACxD;SAAM,IAAI,aAAa,GAAG,CAAC,EAAE;QAC5B,OAAO,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;KACzD;SAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAyB;IACnD,OAAO;QACL,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK;QACzB,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG;KACrC,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAAC,SAAsB;IACpD,IAAI,SAAS,CAAC,IAAI,KAAK,iBAAiB,IAAI,SAAS,CAAC,IAAI,KAAK,gBAAgB,EAAE;QAC/E,OAAO,SAAS,CAAC,QAAQ,IAAI,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC;KAC/E;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,kBAAkB,CAAC,UAA6B;IACvD,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,iBAAiB;QACrC,CAAC,UAAU,CAAC,QAAQ,KAAK,GAAG,IAAI,UAAU,CAAC,QAAQ,KAAK,GAAG,CAAC,CAC7D,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,UAA6B;IACtD,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE;QACjC,OAAO,OAAO,UAAU,CAAC,KAAK,KAAK,QAAQ,IAAI,OAAO,UAAU,CAAC,KAAK,KAAK,QAAQ,CAAC;KACrF;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}