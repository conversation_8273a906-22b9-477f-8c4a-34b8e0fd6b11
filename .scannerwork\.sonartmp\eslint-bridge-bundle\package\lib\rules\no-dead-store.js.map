{"version": 3, "file": "no-dead-store.js", "sourceRoot": "", "sources": ["../../src/rules/no-dead-store.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,iEAK+C;AAC/C,+BAA0D;AAI1D,oCAAgE;AAEnD,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,aAAa,GAAsB,EAAE,CAAC;QAC5C,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAyB,CAAC;QAC1D,MAAM,aAAa,GAAG,IAAI,GAAG,EAAY,CAAC;QAC1C,2DAA2D;QAC3D,MAAM,cAAc,GAAG,IAAI,GAAG,EAAyB,CAAC;QACxD,MAAM,6BAA6B,GAAG,IAAI,GAAG,EAAiB,CAAC;QAC/D,MAAM,kBAAkB,GAA2B,EAAE,CAAC;QAEtD,OAAO;YACL,0DAA0D,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAChF,qBAAqB,CAAC,IAAsB,CAAC,CAAC;YAChD,CAAC;YACD,+DAA+D,EAAE,GAAG,EAAE;gBACpE,oBAAoB,EAAE,CAAC;YACzB,CAAC;YACD,UAAU,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAChC,IAAI,cAAc,EAAE,EAAE;oBACpB,OAAO;iBACR;gBACD,oBAAoB,CAAC,IAAyB,CAAC,CAAC;YAClD,CAAC;YACD,aAAa,EAAE,CAAC,IAAa,EAAE,EAAE;gBAC/B,oBAAoB,CAAC,IAA8B,CAAC,CAAC;YACvD,CAAC;YACD,aAAa,EAAE,GAAG,EAAE;gBAClB,kBAAkB,CAAC,IAAI,CAAC,IAAI,oBAAoB,EAAE,CAAC,CAAC;YACtD,CAAC;YACD,uCAAuC,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAE,CAAC;gBAChD,MAAM,EAAE,GAAG,EAAE,GAAG,gBAAgB,CAAC,IAAyB,CAAC,CAAC;gBAC5D,IAAI,GAAG,EAAE;oBACP,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACpC;YACH,CAAC;YACD,iEAAiE,EAAE,GAAG,EAAE;gBACtE,IAAI,CAAC,kBAAkB,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;YAC1C,CAAC;YACD,oBAAoB,EAAE,GAAG,EAAE;gBACzB,MAAM,aAAa,GAAG,kBAAkB,CAAC,GAAG,EAAE,CAAC;gBAC/C,IAAI,aAAa,IAAI,aAAa,CAAC,OAAO,EAAE;oBAC1C,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;iBACjF;YACH,CAAC;YAED,cAAc,EAAE,GAAG,EAAE;gBACnB,SAAG,CAAC,gBAAgB,CAAC,CAAC;gBACtB,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC7B,YAAY,CAAC,GAAG,CAAC,CAAC;oBAClB,wBAAwB,CAAC,GAAG,CAAC,CAAC;gBAChC,CAAC,CAAC,CAAC;YACL,CAAC;YAED,kBAAkB;YAClB,sBAAsB,EAAE,CAAC,OAAwB,EAAE,EAAE;gBACnD,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,mBAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC/D,CAAC;YACD,eAAe,EAAE,QAAQ,CAAC,EAAE;gBAC1B,WAAW,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC7C,CAAC;YACD,aAAa,EAAE,GAAG,EAAE;gBAClB,UAAU,EAAE,CAAC;YACf,CAAC;SACF,CAAC;QAEF,SAAS,qBAAqB,CAAC,IAAoB;YACjD,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,CAAC;QAED,SAAS,oBAAoB;YAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,GAAG,EAAG,CAAC;YAC9D,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC;QAED,SAAS,YAAY,CAAC,aAA4B;YAChD,MAAM,UAAU,GAAG,IAAI,GAAG,CAAW,aAAa,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,UAAU,GAAG,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;YAC3D,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACvB,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;gBAC9B,IAAI,CAAC,QAAQ,EAAE;oBACb,OAAO;iBACR;gBACD,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE;oBACjB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;wBAClD,MAAM,CAAC,GAAG,CAAC,CAAC;qBACb;oBACD,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;iBAC7B;gBACD,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE;oBAChB,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;iBAC1B;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,SAAS,wBAAwB,CAAC,GAAkB;YAClD,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC3B,IAAI,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,QAAS,CAAC,EAAE;oBACnE,MAAM,CAAC,GAAG,CAAC,CAAC;iBACb;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,SAAS,YAAY,CAAC,GAAkB;YACtC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YAC9B,OAAO,CACL,QAAQ;gBACR,qBAAqB,CAAC,GAAG,CAAC;gBAC1B,CAAC,6BAA6B,CAAC,QAAQ,CAAC;gBACxC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAC5B,CAAC;QACJ,CAAC;QAED,SAAS,qBAAqB,CAAC,GAAkB;YAC/C,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YAC9B,OAAO,CACL,QAAQ;gBACR,UAAU,CAAC,QAAQ,CAAC;gBACpB,CAAC,yBAAyB,CAAC,GAAG,CAAC;gBAC/B,CAAC,kBAAkB,CAAC,GAAG,CAAC;gBACxB,CAAC,6BAA6B,CAAC,GAAG,CAAC,GAAG,CAAC;gBACvC,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAC/B,CAAC;QACJ,CAAC;QAED,SAAS,cAAc;YACrB,OAAQ,OAAO,CAAC,YAAY,EAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,mBAAmB,CAAC,CAAC;QAC/F,CAAC;QAED,SAAS,kBAAkB,CAAC,GAAkB;YAC5C,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE;gBACxC,OAAO,KAAK,CAAC;aACd;YACD,MAAM,MAAM,GAAI,GAAG,CAAC,UAAkC,CAAC,MAAM,CAAC;YAC9D,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,mBAAmB,CAAC;QACvD,CAAC;QAED,SAAS,UAAU,CAAC,QAAkB;YACpC,aAAa;YACb,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,KAAsB,CAAC;YAC1C,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAqB,CAAC;QACxE,CAAC;QAED,SAAS,6BAA6B,CAAC,QAAwB;YAC7D,OAAO,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAChD,CAAC;QAED,SAAS,yBAAyB,CAAC,GAAkB;YACnD,OAAO,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,SAAS,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAClE,CAAC;QAED,SAAS,YAAY,CAAC,IAAiB;YACrC,IAAI,iBAAS,CAAC,IAAI,CAAC,EAAE;gBACnB,OAAO,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAY,CAAC,CAAC;aACnF;YACD,IAAI,oBAAY,CAAC,IAAI,CAAC,EAAE;gBACtB,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC;aAClC;YACD,IAAI,yBAAiB,CAAC,IAAI,CAAC,EAAE;gBAC3B,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACpC;YACD,IAAI,0BAAkB,CAAC,IAAI,CAAC,EAAE;gBAC5B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;aACrC;YACD,IAAI,yBAAiB,CAAC,IAAI,CAAC,EAAE;gBAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;aACnC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,MAAM,CAAC,GAAkB;YAChC,OAAO,CAAC,MAAM,CAAC;gBACb,OAAO,EAAE,+CAA+C,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI;gBAC/E,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,GAAI;aACzB,CAAC,CAAC;QACL,CAAC;QAED,SAAS,oBAAoB,CAAC,IAAgD;YAC5E,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GACrB,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAClF,IAAI,GAAG,EAAE;gBACP,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBACtB,IAAI,QAAQ,EAAE;oBACZ,mBAAmB,CAAC,GAAG,CAAC,CAAC;iBAC1B;aACF;YACD,IAAI,QAAQ,EAAE;gBACZ,oBAAoB,CAAC,QAAQ,CAAC,CAAC;aAChC;QACH,CAAC;QAED,SAAS,mBAAmB,CAAC,IAA4B;YACvD,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;gBAC5B,OAAO,EAAE,CAAC;aACX;YACD,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAChE,OAAO,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC;QAChE,CAAC;QAED,SAAS,kBAAkB,CAAC,IAA4B;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,cAAc,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;QAC1E,CAAC;QAED,SAAS,gBAAgB,CAAC,GAAkB;YAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC;YAC5D,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;gBACzC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACrB;iBAAM;gBACL,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC7D,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,aAAa,CAAC,OAAwB;YAC7C,IAAI,GAAG,CAAC;YACR,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACpC,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAE,CAAC;aACzC;iBAAM;gBACL,GAAG,GAAG,IAAI,mBAAa,CAAC,OAAO,CAAC,CAAC;gBACjC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;aACvC;YACD,OAAO,GAAG,CAAC;QACb,CAAC;QAED,SAAS,mBAAmB,CAAC,SAAwB;YACnD,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAS,CAAC;YACrC,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE;gBACtB,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;aAC7B;QACH,CAAC;QAED,SAAS,oBAAoB,CAAC,QAAwB;YACpD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,IAAI,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAChC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;aAC/C;iBAAM;gBACL,cAAc,CAAC,GAAG,CAChB,QAAQ,EACR,IAAI,GAAG,CAAS,CAAC,UAAU,CAAC,CAAC,CAC9B,CAAC;aACH;QACH,CAAC;QAED,SAAS,UAAU;YACjB,aAAa,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,SAAS,WAAW,CAAC,eAAgC;YACnD,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAuB;YAC/C,OAAO,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAuB,EACvB,KAAyB;YAEzB,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;aACtC;YACD,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC;YAC9D,IAAI,GAAG,EAAE;gBACP,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;aACxC;iBAAM;gBACL,0EAA0E;gBAC1E,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;gBAClF,IAAI,QAAQ,EAAE;oBACZ,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;iBAChC;gBACD,wGAAwG;gBACxG,qDAAqD;gBACrD,OAAO,2BAA2B,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;aACvD;QACH,CAAC;IACH,CAAC;CACF,CAAC;AAEF,MAAM,eAAe;IAOnB,YAAY,QAAkB;QAN9B,qBAAgB,GAAG,IAAI,GAAG,EAAyB,CAAC;QACpD,uBAAkB,GAAoB,EAAE,CAAC;QAEzC,aAAQ,GAAG,IAAI,GAAG,EAA2B,CAAC;QAC9C,oBAAe,GAAwB,EAAE,CAAC;QAGxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;CACF;AAED,MAAM,oBAAoB;IAA1B;QACE,YAAO,GAAG,KAAK,CAAC;QAChB,eAAU,GAAoB,EAAE,CAAC;IACnC,CAAC;CAAA;AAID,MAAM,iBAAiB;IAGrB,YAAY,IAAoB;QAIhC,QAAG,GAAG,IAAI,GAAG,EAAiB,CAAC;QAC/B,QAAG,GAAG,IAAI,GAAG,EAAiB,CAAC;QAJ7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAKD,KAAK,CAAC,IAAmB;QACvB,OAAO,8BAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IAChG,CAAC;IAED,KAAK,CAAC,IAAmB;QACvB,OAAO,8BAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;IAC7F,CAAC;IAED,GAAG,CAAC,GAAkB;QACpB,IAAI,MAAM,GAAG,GAAG,CAAC,UAAuC,CAAC;QACzD,OAAO,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClB,MAAM;aACP;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClB,MAAM;aACP;YACD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SACxB;QACD,IAAI,MAAM,KAAK,IAAI,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACtD;IACH,CAAC;CACF;AAED,MAAM,YAAY;IAOhB,YAAY,IAA4B,EAAE,KAAkB;QAJ5D,SAAI,GAAG,KAAK,CAAC;QAEb,cAAS,GAAuB,IAAI,CAAC;QAGnC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO;QACL,OAAO,KAAK,CAAC;IACf,CAAC;IAED,WAAW;QACT,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED,SAAS,sBAAsB,CAC7B,IAA4B,EAC5B,KAAyB;IAEzB,OAAO,CACL,KAAK;QACL,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAC/F,CAAC;AACJ,CAAC;AAED,SAAS,IAAI,CAAI,GAAa;IAC5B,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7B,CAAC"}