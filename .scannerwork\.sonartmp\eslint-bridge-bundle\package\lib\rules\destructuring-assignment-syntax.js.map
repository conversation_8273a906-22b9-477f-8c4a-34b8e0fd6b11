{"version": 3, "file": "destructuring-assignment-syntax.js", "sourceRoot": "", "sources": ["../../src/rules/destructuring-assignment-syntax.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,oCAAuE;AAEvE,MAAM,SAAS,GAAG,CAAC,CAAC;AACpB,MAAM,cAAc,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,SAAS,CAAC;AAExD,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IAED,MAAM,CAAC,OAAyB;QAC9B,SAAS,eAAe,CAAC,UAA8D;YACrF,MAAM,oBAAoB,GAA6C,IAAI,GAAG,EAAE,CAAC;YAEjF,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;gBAClC,IAAI,SAAS,CAAC,IAAI,KAAK,qBAAqB,EAAE;oBAC5C,iBAAiB,CAAC,oBAAoB,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;iBACjE;qBAAM;oBACL,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;oBAC7C,oBAAoB,CAAC,KAAK,EAAE,CAAC;iBAC9B;aACF;YACD,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;QAC/C,CAAC;QAED,SAAS,iBAAiB,CACxB,oBAA8D,EAC9D,YAA8C;YAE9C,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;gBACtC,MAAM,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC;gBAC1B,IAAI,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,YAAY,EAAE;oBAChD,MAAM,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC;oBACxB,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC;oBACpC,IAAI,UAAU,CAAC,IAAI,KAAK,kBAAkB,EAAE;wBAC1C,SAAS;qBACV;oBACD,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;oBACrC,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE;wBAC/D,cAAc,CAAC,oBAAoB,EAAE,UAAU,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;qBACtE;yBAAM,IACL,QAAQ,CAAC,IAAI,KAAK,SAAS;wBAC3B,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;wBAClC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,EAC9B;wBACA,cAAc,CAAC,oBAAoB,EAAE,UAAU,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;qBACtE;iBACF;aACF;QACH,CAAC;QAED,SAAS,cAAc,CACrB,oBAA8D,EAC9D,MAAmB,EACnB,WAAsC;YAEtC,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,KAAK,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,KAAK,EAAE;gBACT,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACzB;iBAAM;gBACL,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;aAC9C;QACH,CAAC;QAED,SAAS,sBAAsB,CAC7B,oBAA8D;YAE9D,oBAAoB,CAAC,OAAO,CAAC,CAAC,YAAyC,EAAE,GAAW,EAAE,EAAE;gBACtF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC3B,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE;wBACnD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;4BACrB,OAAO,EAAE,wBAAgB,CACvB,wDAAwD,GAAG,IAAI,EAC/D,IAAuB,EACvB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CACpD;yBACF,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,eAAe,CAAE,IAA8B,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC;YACD,UAAU,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAChC,eAAe,CAAE,IAA0B,CAAC,UAAU,CAAC,CAAC;YAC1D,CAAC;YACD,OAAO,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC7B,eAAe,CAAE,IAAuB,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,OAAO,CAAC,UAAqC;IACpD,MAAM,WAAW,GAAG,iCAAyB,CAC3C,UAA2B,EAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,qBAAqB,CACI,CAAC;IAC5C,OAAO,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC;AACzC,CAAC"}