{"version": 3, "file": "reachingDefinitions.js", "sourceRoot": "", "sources": ["../../src/rules/reachingDefinitions.ts"], "names": [], "mappings": ";;;AA2BA,MAAM,cAAe,SAAQ,GAAiB;IAA9C;;QACE,SAAI,GAAqB,gBAAgB,CAAC;IAC5C,CAAC;CAAA;AAED,MAAM,cAAc,GAAG,CAAC,GAAiB,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAI3D,QAAA,YAAY,GAAiB;IACxC,IAAI,EAAE,cAAc;CACrB,CAAC;AAIF,SAAgB,mBAAmB,CAAC,sBAAwD;IAC1F,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAEnF,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;QAC1B,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,EAAG,CAAC;QAChC,MAAM,YAAY,GAAG,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAE,CAAC;QAC7D,MAAM,aAAa,GAAG,YAAY,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;QACrE,IAAI,aAAa,EAAE;YACjB,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC3D;KACF;AACH,CAAC;AAXD,kDAWC;AAED,MAAa,mBAAmB;IAC9B,YAAY,OAA6B;QAMzC,OAAE,GAAG,IAAI,GAAG,EAAoB,CAAC;QAEjC,QAAG,GAAG,IAAI,GAAG,EAAoB,CAAC;QAElC;;WAEG;QACH,eAAU,GAAG,IAAI,GAAG,EAAa,CAAC;QAZhC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAaD,GAAG,CAAC,GAAc;QAChB,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;QAC9B,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC1B;IACH,CAAC;IAED,SAAS,CAAC,sBAAwD;QAChE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QAChB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAE,CAAC,GAAG,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAmB,IAAI,CAAC,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;YAC7B,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;YAClB,OAAO,IAAI,CAAC;SACb;aAAM;YACL,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,kBAAkB,CAAC,GAAc,EAAE,YAAmC;QACpE,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;QAC9B,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;YAClB,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,oBAAY,CAAC,CAAC;YACzC,OAAO;SACR;QACD,MAAM,SAAS,GAAG,qBAAqB,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QACzF,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,CAAC,WAAkC;QACrC,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,cAAc,EAAE,CAAC;YAC1D,IAAI,QAAQ,CAAC,IAAI,KAAK,gBAAgB,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE;gBAC1E,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;aAC5B;iBAAM;gBACL,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,oBAAY,CAAC,CAAC;aAChC;SACF;IACH,CAAC;CACF;AA9DD,kDA8DC;AAED,SAAgB,qBAAqB,CACnC,WAAqB,EACrB,SAA6B,EAC7B,iBAAwC,EACxC,KAAkB;IAElB,IAAI,CAAC,SAAS,EAAE;QACd,OAAO,oBAAY,CAAC;KACrB;IACD,QAAQ,SAAS,CAAC,IAAI,EAAE;QACtB,KAAK,SAAS;YACZ,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,oBAAY,CAAC;QACtE,KAAK,YAAY;YACf,MAAM,WAAW,GAAG,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,WAAW,IAAI,WAAW,KAAK,WAAW,EAAE;gBAC9C,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAClE,OAAO,sBAAsB,IAAI,oBAAY,CAAC;aAC/C;YACD,OAAO,oBAAY,CAAC;QACtB;YACE,OAAO,oBAAY,CAAC;KACvB;AACH,CAAC;AAtBD,sDAsBC;AAED,SAAS,MAAM,CAAC,GAA0B,EAAE,GAA0B;IACpE,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IACD,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,GAAG,EAAE;QACrC,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,YAAY,CAAC,CAAS,EAAE,CAAS;IACxC,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAgB,EAAE;QAC9D,OAAO,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACxB;IACD,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,CAAC;AAED,SAAS,SAAS,CAAI,CAAS,EAAE,CAAS;IACxC,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,SAAgB,yBAAyB,CAAC,UAA6B,EAAE,KAAkB;IACzF,IAAI,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC;IAC7E,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,EAAE;QAC5B,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC;KAChF;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAND,8DAMC"}