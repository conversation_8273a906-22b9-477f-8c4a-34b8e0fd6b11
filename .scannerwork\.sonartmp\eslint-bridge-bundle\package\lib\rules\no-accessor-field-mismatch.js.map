{"version": 3, "file": "no-accessor-field-mismatch.js", "sourceRoot": "", "sources": ["../../src/rules/no-accessor-field-mismatch.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAKjD,oCAA4C;AAY/B,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IAED,MAAM,CAAC,OAAyB;QAC9B,MAAM,kBAAkB,GAAG,CAAC,IAAI,GAAG,EAAiB,CAAC,CAAC;QAEtD,SAAS,aAAa,CAAC,QAAuD;YAC5E,MAAM,gBAAgB,GACpB,QAAQ,CAAC,IAAI,KAAK,kBAAkB,IAAI,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC;YAC9E,MAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACnD,IAAI,CAAC,YAAY,IAAI,CAAC,gBAAgB,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC9E,OAAO;aACR;YAED,MAAM,cAAc,GAAG,kBAAkB,CACvC,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EACjD,YAAY,CAAC,IAAI,CAClB,CAAC;YACF,IACE,cAAc,CAAC,MAAM,GAAG,CAAC;gBACzB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;oBACtB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,EAC3E;gBACA,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,cAAc,GAClB,iBAAiB,YAAY,CAAC,IAAI,GAAG;oBACrC,+CAA+C,YAAY,CAAC,IAAI,IAAI,CAAC;gBACvE,MAAM,kBAAkB,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC/C,MAAM,iBAAiB,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAEjE,OAAO,CAAC,MAAM,CAAC;oBACb,OAAO,EAAE,wBAAgB,CAAC,cAAc,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;oBAChF,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG;iBACtB,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,CAAC,IAAiB,EAAE,EAAE,CAAC,aAAa,CAAC,IAAyB,CAAC;YACzE,gBAAgB,EAAE,CAAC,IAAiB,EAAE,EAAE,CAAC,aAAa,CAAC,IAAiC,CAAC;YAEzF,SAAS,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAC/B,MAAM,SAAS,GAAG,IAA0B,CAAC;gBAC7C,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE,CACxD,CAAC,YAAY,CAAC,IAAI,KAAK,eAAe;oBACpC,YAAY,CAAC,IAAI,KAAK,yBAAyB,CAAC;oBAClD,CAAC,YAAY,CAAC,MAAM;oBAClB,CAAC,CAAC,YAAY,CAAC,GAAG;oBAClB,CAAC,CAAC,IAAI,CACT,CAAC;gBACF,MAAM,qBAAqB,GAAG,qCAAqC,CAAC,SAAS,CAAC,CAAC;gBAC/E,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,qBAAqB,CAAC,CAAC,CAAC;gBACjE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;YACD,gBAAgB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACtC,MAAM,aAAa,GAAG,WAAW,CAAE,IAAkC,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CACvF,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAC3C,CAAC;gBACF,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzC,CAAC;YACD,4CAA4C,EAAE,GAAG,EAAE;gBACjD,kBAAkB,CAAC,GAAG,EAAE,CAAC;YAC3B,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,eAAe,CACtB,QAAuD;IAEvD,IAAI,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,IAAI,CAAC;KACb;IAED,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAC1B,IAAI,QAAQ,CAAC,IAAI,KAAK,KAAK,EAAE;QAC3B,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;KACjC;SAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,KAAK,EAAE;QAClC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;KACjC;SAAM;QACL,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC7C;AACH,CAAC;AAED,SAAS,OAAO,CAAC,GAAkB;IACjC,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE;QAC1B,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KAC1B;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE;QACpC,OAAO,GAAG,CAAC,IAAI,CAAC;KACjB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,cAAc,CAAC,IAAY,EAAE,kBAAiC;IACrE,IAAI,kBAAkB,CAAC,IAAI,KAAK,oBAAoB,EAAE;QACpD,OAAO,IAAI,CAAC;KACb;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,kBAAkB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACpE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;KACpD;IACD,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,kBAAkB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACpE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;KACpD;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,WAAW,CAClB,QAAa,EACb,eAAyD;IAEzD,MAAM,MAAM,GAAuB,IAAI,GAAG,EAAiB,CAAC;IAC5D,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,MAAM,gBAAgB,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,gBAAgB,EAAE;YACpB,MAAM,IAAI,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACvC,IAAI,IAAI,EAAE;gBACR,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;oBAC7B,IAAI;oBACJ,IAAI,EAAE,OAAO;iBACd,CAAC,CAAC;aACJ;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAmB;IAC7C,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;AAC1E,CAAC;AAED,SAAS,qCAAqC,CAAC,eAAmC;IAChF,MAAM,MAAM,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;IACjD,IAAI,MAAM,EAAE;QACV,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAiB,CAAC;QACvD,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,EAAE;YACrC,IACE,SAAS,CAAC,IAAI,KAAK,qBAAqB;gBACxC,CAAC,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,QAAQ,CAAC,EAC/C;gBACA,MAAM,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACnD,IAAI,aAAa,EAAE;oBACjB,qBAAqB,CAAC,GAAG,CAAC,aAAa,EAAE;wBACvC,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,SAAS;qBAChB,CAAC,CAAC;iBACJ;aACF;SACF;QACD,OAAO,qBAAqB,CAAC;KAC9B;SAAM;QACL,OAAO,IAAI,GAAG,EAAiB,CAAC;KACjC;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,eAAmC;IAEnC,KAAK,MAAM,YAAY,IAAI,eAAe,CAAC,IAAI,EAAE;QAC/C,IAAI,YAAY,CAAC,IAAI,KAAK,kBAAkB,IAAI,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,aAAa,EAAE;YAC3F,OAAO,YAAY,CAAC,KAAK,CAAC;SAC3B;KACF;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,aAAiC,EAAE,IAAY;IACzE,MAAM,sBAAsB,GAAG,IAAI,IAAI,EAAE,CAAC;IAC1C,MAAM,sBAAsB,GAAG,GAAG,IAAI,GAAG,CAAC;IAC1C,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACvE,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACvE,OAAO,CAAC,cAAc,EAAE,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,MAAM,CACxE,KAAK,CAAC,EAAE,CAAC,KAAK,CACJ,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CAAC,IAAmB;IAC1C,IAAI,IAAI,CAAC,IAAI,KAAK,oBAAoB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACpD,OAAO,IAAI,CAAC;KACb;IACD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,CAAC;AAED,SAAS,eAAe,CAAC,UAAsC;IAC7D,IACE,UAAU;QACV,UAAU,CAAC,IAAI,KAAK,kBAAkB;QACtC,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAC3C;QACA,OAAO,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;KACrC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,4BAA4B,CAAC,SAA6B,EAAE,YAA0B;IAC7F,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE;QAClC,IACE,SAAS,CAAC,IAAI,KAAK,qBAAqB;YACxC,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAsB,EACpD;YACA,OAAO,eAAe,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACnD;KACF;SAAM,IAAI,SAAS,CAAC,IAAI,KAAK,iBAAiB,EAAE;QAC/C,OAAO,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;KAC5C;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,0BAA0B,CACjC,SAA6B,EAC7B,YAA0B,EAC1B,cAAuB;IAEvB,MAAM,SAAS,GAAG,4BAA4B,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACxE,OAAO,CAAC,SAAS,IAAI,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,KAAK,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9F,CAAC"}