{"version": 3, "file": "xml-parser-xxe.js", "sourceRoot": "", "sources": ["../../src/rules/xml-parser-xxe.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAGjD,yEAA6E;AAE7E,oCAA+F;AAE/F,MAAM,WAAW,GAAG,UAAU,CAAC;AAC/B,MAAM,WAAW,GAAG,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;AAEtC,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,SAAS,eAAe,CAAC,IAA2B;YAClD,OAAO,CACL,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC5E,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB;oBACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;oBAC1C,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAC1B,CAAC;QACJ,CAAC;QAED,SAAS,mBAAmB;YAC1B,OAAO,oBAAoB,EAAE,IAAI,oBAAoB,EAAE,CAAC;QAC1D,CAAC;QAED,SAAS,oBAAoB;YAC3B,OAAO,6BAAqB,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5F,CAAC;QAED,SAAS,oBAAoB;YAC3B,OAAO,CACL,uBAAe,CAAC,OAAO,CAAC,CAAC,SAAS,CAChC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,WAAW,CAC/E,GAAG,CAAC,CAAC,CACP,CAAC;QACJ,CAAC;QAED,SAAS,UAAU,CAAC,QAAyB;YAC3C,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC;QAC5E,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,IAAI,GAAG,IAA6B,CAAC;gBAC3C,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,mBAAmB,EAAE,EAAE;oBAClD,MAAM,KAAK,GAAG,mCAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;oBACtE,IAAI,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;wBAC9B,OAAO,CAAC,MAAM,CAAC;4BACb,OAAO,EAAE,4BAAgB,CAAC,qDAAqD,EAAE;gCAC/E,IAAI,CAAC,MAAM;6BACZ,CAAC;4BACF,IAAI,EAAE,KAAK;yBACZ,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}