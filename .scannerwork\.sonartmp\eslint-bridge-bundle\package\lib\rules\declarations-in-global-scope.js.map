{"version": 3, "file": "declarations-in-global-scope.js", "sourceRoot": "", "sources": ["../../src/rules/declarations-in-global-scope.ts"], "names": [], "mappings": ";;;AAsBA,oCAAwC;AAE3B,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,OAAO;gBACL,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACjC,8GAA8G;gBAC9G,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;gBAC7C,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;;oBACxC,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,EAAE;wBACjE,8DAA8D;wBAC9D,OAAO;qBACR;oBACD,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE;wBAC/B,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;wBACzB,IACE,GAAG,CAAC,IAAI,KAAK,cAAc;4BAC3B,CAAC,GAAG,CAAC,IAAI,KAAK,UAAU,IAAI,CAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,IAAI,MAAK,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EACpF;4BACA,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,OAAO;gCACb,OAAO,EACL,gGAAgG;6BACnG,CAAC,CAAC;4BACH,OAAO;yBACR;qBACF;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,eAAe,CAAC,OAAyB;IAChD,OAAO,OAAO,CAAC,aAAa,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AACpF,CAAC;AAED,SAAS,SAAS,CAAC,IAAoC;IACrD,OAAO,CACL,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,MAAK,gBAAgB;QAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;QAC3B,oBAAY,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CACrC,CAAC;AACJ,CAAC"}