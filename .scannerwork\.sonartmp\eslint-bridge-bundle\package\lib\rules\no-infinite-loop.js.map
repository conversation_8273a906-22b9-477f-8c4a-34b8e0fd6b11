{"version": 3, "file": "no-infinite-loop.js", "sourceRoot": "", "sources": ["../../src/rules/no-infinite-loop.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAEjD,mCAAsC;AAEtC,oCAAuD;AAEvD,MAAM,MAAM,GAAG,IAAI,eAAM,EAAE,CAAC;AAC5B,MAAM,sBAAsB,GAAG,uBAAe,CAC5C,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,8BAA8B,CAAE,EACtD,QAAQ,CACT,CAAC;AACF,MAAM,OAAO,GAAG,wDAAwD,CAAC;AAC5D,QAAA,IAAI,GAAoB;IACnC,4CAA4C;IAC5C,IAAI,EAAE,sBAAsB,CAAC,IAAI;IACjC,MAAM,CAAC,OAAyB;QAC9B,MAAM,wBAAwB,GAAG,sBAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACrC,CAAC;YACD,gBAAgB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACtC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACrC,CAAC;YACD,YAAY,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAClC,MAAM,YAAY,GAAG,IAA2B,CAAC;gBACjD,IACE,CAAC,YAAY,CAAC,IAAI;oBAClB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAC1E;oBACA,MAAM,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBAChF,IAAI,CAAC,eAAe,EAAE;wBACpB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;wBAC/D,OAAO,CAAC,MAAM,CAAC;4BACb,GAAG,EAAE,UAAW,CAAC,GAAG;4BACpB,OAAO,EAAE,OAAO;yBACjB,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;YACD,cAAc;gBACZ,aAAa;gBACb,wBAAwB,CAAC,cAAc,CAAE,EAAE,CAAC;YAC9C,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,mBAAmB,CAAC,IAAiB,EAAE,OAAyB;IACvE,MAAM,cAAc,GAAG,IAAuD,CAAC;IAC/E,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;QAChF,MAAM,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAClF,IAAI,CAAC,eAAe,EAAE;YACpB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC/D,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,UAAW,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;SAC5D;KACF;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,OAAyB,EAAE,gBAAuC;IAClF,IAAI,MAAM,IAAI,gBAAgB,EAAE;QAC9B,MAAM,IAAI,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;YAC3D,OAAO;SACR;QACD,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;KAClC;AACH,CAAC;AAED,MAAM,WAAW;IAAjB;QACE,oBAAe,GAAG,KAAK,CAAC;IAqC1B,CAAC;IAnCC,MAAM,CAAC,eAAe,CAAC,IAAiB,EAAE,OAAyB;QACjE,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7B,OAAO,OAAO,CAAC,eAAe,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,IAAiB,EAAE,OAAyB;QACxD,MAAM,SAAS,GAAG,CAAC,IAAiB,EAAE,YAAY,GAAG,KAAK,EAAE,EAAE;YAC5D,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,cAAc;oBACjB,YAAY,GAAG,IAAI,CAAC;oBACpB,MAAM;gBACR,KAAK,oBAAoB,CAAC;gBAC1B,KAAK,qBAAqB;oBACxB,kCAAkC;oBAClC,OAAO;gBACT,KAAK,gBAAgB;oBACnB,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE;wBACjC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;qBAC7B;oBACD,MAAM;gBACR,KAAK,iBAAiB,CAAC;gBACvB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,gBAAgB;oBACnB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,OAAO;aACV;YACD,kBAAU,CAAC,IAAI,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CACpE,SAAS,CAAC,KAAK,EAAE,YAAY,CAAC,CAC/B,CAAC;QACJ,CAAC,CAAC;QACF,SAAS,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;CACF"}