{"version": 3, "file": "deprecation.js", "sourceRoot": "", "sources": ["../../src/rules/deprecation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAKjD,oCAA4E;AAC5E,+CAAiC;AACjC,iEAAkE;AAErD,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QACD,OAAO;YACL,UAAU,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAChC,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;gBAClC,IAAI,mBAAmB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG,KAAK,IAAI,EAAE;oBACtD,sBAAsB;oBACtB,OAAO;iBACR;gBACD,IAAI,0BAA0B,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;oBAC7C,OAAO;iBACR;gBACD,MAAM,EAAE,GAAG,IAAyB,CAAC;gBACrC,MAAM,kBAAkB,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC3F,IAAI,kBAAkB,IAAI,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;oBACpD,OAAO;iBACR;gBAED,MAAM,WAAW,GAAG,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,WAAW,EAAE;oBACf,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,OAAO,EAAE,IAAI,EAAE,CAAC,IAAI,oBAAoB,WAAW,CAAC,MAAM,EAAE;qBAC7D,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,aAAa,CAAC,EAAqB,EAAE,OAAyB;IACrE,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,mBAAmB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,KAAK,EAAE,EAAE;QACtD,OAAO,KAAK,CAAC;KACd;IAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;IAC5E,IAAI,QAAQ,EAAE;QACZ,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;KACnD;IAED,MAAM,gBAAgB,GAAG;QACvB,eAAe;QACf,qBAAqB;QACrB,mBAAmB;QACnB,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;KACpB,CAAC;IACF,OAAO,MAAM,IAAI,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,cAAc,CACrB,EAAqB,EACrB,QAAgC,EAChC,OAAyB;IAEzB,MAAM,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IAC7C,MAAM,cAAc,GAAG,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAEtD,IAAI,cAAc,EAAE;QAClB,MAAM,gBAAgB,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAA+B,CAAC,CAAC;QAC7F,MAAM,SAAS,GAAG,EAAE,CAAC,oBAAoB,CAAC,gBAAyC,CAAC,CAAC;QACrF,IAAI,SAAS,EAAE;YACb,MAAM,WAAW,GAAG,mBAAmB,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC;YAClE,IAAI,WAAW,EAAE;gBACf,OAAO,WAAW,CAAC;aACpB;SACF;KACF;IACD,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;IAEpD,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,cAAc,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;QACxC,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,SAAS,CAChB,EAAqB,EACrB,QAAgC,EAChC,OAAyB,EACzB,EAAkB;IAElB,IAAI,MAA6B,CAAC;IAClC,MAAM,IAAI,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAmB,CAAkB,CAAC;IACtF,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,iBAAS,CAAC,OAAO,CAAkB,CAAY,CAAC;IAClG,IAAI,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,EAAE;QAChD,MAAM,GAAG,EAAE,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACrE;SAAM,IACL,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;QACtD,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,EAC/D;QACA,IAAI;YACF,MAAM,GAAG,EAAE,CAAC,0CAA0C,CAAC,IAAI,CAAC,CAAC;SAC9D;QAAC,OAAO,CAAC,EAAE;YACV,0DAA0D;YAC1D,+CAA+C;SAChD;KACF;SAAM;QACL,MAAM,GAAG,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;KACvC;IAED,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACzD,OAAO,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;KACpC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iBAAiB,CACxB,OAAyB,EACzB,EAAe;IAEf,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;IACzC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAEhF,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,MAAM,CAAC,QAAQ,KAAK,EAAE,EAAE;QAC1E,MAAM,GAAG,MAAM,CAAC;QAChB,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;KAC7E;IAED,IAAI,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;QACpC,OAAO,MAAM,CAAC;KACf;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,IAA6B,EAC7B,MAAmB;IAEnB,IAAI,IAAI,EAAE;QACR,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE;YACnE,OAAO,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC;SAC/B;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,0BAA0B,EAAE;YACnD,OAAO,IAAI,CAAC,GAAG,KAAK,MAAM,CAAC;SAC5B;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAuB;IAClD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE;YAC7B,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;SAC5D;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,UAAU,CAAC,MAAiB;IACnC,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC;IAChC,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3D,OAAO,KAAK,CAAC;KACd;IACD,QAAQ,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5B,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;QACrC,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC;QACvC,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC;QACtC,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe;YAChC,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAa;IACzC,OAAO,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC;AACxD,CAAC;AAED,SAAS,6BAA6B,CAAC,IAAa;IAClD,OAAO,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,2BAA2B,CAAC;AACjE,CAAC;AAED,SAAS,mBAAmB,CAAC,MAA+B;IAC1D,OAAO,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,SAAS,CAAC;AACpE,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAiB,EAAE,OAAyB;IAC9E,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;IACzC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;IAC/B,MAAM,WAAW,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;IACpC,OAAO,CACL,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,UAAU;QAC3B,CAAC,MAAM,CAAC,QAAQ;QAChB,CAAC,MAAM,CAAC,SAAS;QACjB,MAAM,CAAC,GAAG,KAAK,IAAI;QACnB,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,MAAK,kBAAkB,CACzC,CAAC;AACJ,CAAC;AAED,MAAM,WAAW;IAAjB;QACE,WAAM,GAAG,EAAE,CAAC;IACd,CAAC;CAAA"}