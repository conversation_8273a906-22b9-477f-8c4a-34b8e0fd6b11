{"version": 3, "file": "sonar-no-fallthrough.js", "sourceRoot": "", "sources": ["../../src/rules/sonar-no-fallthrough.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,gDAAgD;;;AAIhD,iEAAkE;AAErD,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,IAAI,eAAe,GAAyB,IAAI,CAAC;QACjD,IAAI,kBAAkB,GAAgC,IAAI,CAAC;QAC3D,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAC/B,MAAM,gBAAgB,GAAgB,IAAI,GAAG,EAAE,CAAC;QAChD,MAAM,0BAA0B,GAAiD,IAAI,GAAG,EAAE,CAAC;QAC3F,MAAM,eAAe,GAAwB,EAAE,CAAC;QAEhD,SAAS,SAAS,CAAC,IAAiB;YAClC,OAAO,OAAO,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;QACrE,CAAC;QAED,SAAS,sBAAsB,CAC7B,OAA6B,EAC7B,cAAoC;YAEpC,MAAM,KAAK,GAAG,EAAE,CAAC;YACjB,MAAM,eAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;YAC/C,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpB,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;gBAC7B,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAChC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACrC,IAAI,OAAO,KAAK,cAAc,EAAE;wBAC9B,OAAO,KAAK,CAAC;qBACd;oBACD,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC1F;aACF;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,eAAe,CAAC,QAAuB;gBACrC,eAAe,GAAG,QAAQ,CAAC;YAC7B,CAAC;YACD,aAAa;gBACX,eAAe,GAAG,eAAgB,CAAC,KAAK,CAAC;YAC3C,CAAC;YACD,sBAAsB,CAAC,OAA6B;gBAClD,kBAAkB,GAAG,OAAO,CAAC;gBAC7B,IAAI,kBAAkB,EAAE;oBACtB,0BAA0B,CAAC,GAAG,CAC5B,eAAe,CAAC,GAAG,EAAuB,EAC1C,kBAAkB,CACnB,CAAC;oBACF,kBAAkB,GAAG,KAAK,CAAC;iBAC5B;YACH,CAAC;YACD,cAAc,CAAC,IAAiB;gBAC9B,MAAM,QAAQ,GAAG,IAA6B,CAAC;gBAC/C,IAAI,iBAAiB,CAAC,QAAQ,CAAC,EAAE;oBAC/B,gBAAgB,CAAC,GAAG,CAAC,kBAAmB,CAAC,EAAE,CAAC,CAAC;iBAC9C;YACH,CAAC;YACD,UAAU,CAAC,IAAiB;gBAC1B,kBAAkB,GAAG,IAAI,CAAC;gBAC1B,eAAe,CAAC,IAAI,CAAC,IAAyB,CAAC,CAAC;YAClD,CAAC;YACD,iBAAiB,CAAC,IAAiB;gBACjC,MAAM,UAAU,GAAG,IAAyB,CAAC;gBAC7C,MAAM,cAAc,GAAyB,0BAA0B,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;gBACzF,MAAM,WAAW,GAAG,eAAgB,CAAC,eAAe,CAAC,IAAI,CACvD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,cAAc,CAAC,CAC/D,CAAC;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,iBAAS,CAAC,OAAO,CAA2B,CAAC;gBAC/D,IACE,WAAW;oBACX,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;oBAChC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI;oBAChC,SAAS,CAAC,UAAU,CAAC,EACrB;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EACL,wFAAwF;wBAC1F,GAAG,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,GAAG;qBACtD,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,iBAAiB,CAAC,QAA+B;IACxD,OAAO,CACL,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB;QAC3C,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY;QAC5C,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS;QACzC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;QAC9C,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,MAAM,CACzC,CAAC;AACJ,CAAC"}