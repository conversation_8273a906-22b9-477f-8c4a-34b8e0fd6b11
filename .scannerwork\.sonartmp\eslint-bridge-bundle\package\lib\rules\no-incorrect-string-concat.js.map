{"version": 3, "file": "no-incorrect-string-concat.js", "sourceRoot": "", "sources": ["../../src/rules/no-incorrect-string-concat.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAIjD,oDAAsC;AACtC,oCAKkB;AAElB,MAAM,OAAO,GAAG,wEAAwE,CAAC;AACzF,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AAEzC,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAA2B,OAAO,CAAC,cAAc,CAAC;QAEhE,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,qBAAqB,CAAC,KAAmB,EAAE,KAAmB;YACrE,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;gBAChD,OAAO,KAAK,CAAC;aACd;YACD,MAAM,YAAY,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,kFAAkF;YAClF,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC3F,CAAC;QAED,SAAS,mBAAmB,CAAC,IAAiB,EAAE,KAAkB;YAChE,OAAO,OAAO;iBACX,aAAa,EAAE;iBACf,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;iBAC7B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAAE,CAAC,GAAG,CAAC;QAC7C,CAAC;QAED,OAAO;YACL,gCAAgC,CAAC,IAAiB;gBAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAA+B,CAAC;gBACxD,IACE,eAAe,CAAC,IAAI,CAAC;oBACrB,eAAe,CAAC,KAAK,CAAC;oBACtB,eAAe,CAAC,IAAI,CAAC;oBACrB,eAAe,CAAC,KAAK,CAAC,EACtB;oBACA,OAAO;iBACR;gBAED,MAAM,QAAQ,GAAG,2BAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACrD,MAAM,SAAS,GAAG,2BAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACvD,IACE,qBAAqB,CAAC,QAAQ,EAAE,SAAS,CAAC;oBAC1C,qBAAqB,CAAC,SAAS,EAAE,QAAQ,CAAC,EAC1C;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,wBAAgB,CACvB,OAAO,EACP,CAAC,IAAI,EAAE,KAAK,CAAC,EACb;4BACE,yBAAyB,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;4BAC1D,0BAA0B,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG;yBAC7D,CACF;wBACD,GAAG,EAAE,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC;qBACtC,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,YAAY,CAAC,GAAiB;IACrC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,aAAa,CAAC,IAAkB;IACvC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C;IACD,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;AAChC,CAAC;AAED,SAAS,eAAe,CAAC,IAAiB;IACxC,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;AACnE,CAAC;AAED,SAAS,eAAe,CAAC,IAAiB;IACxC,OAAO,IAAI,CAAC,IAAI,KAAK,kBAAkB,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;AACnE,CAAC"}