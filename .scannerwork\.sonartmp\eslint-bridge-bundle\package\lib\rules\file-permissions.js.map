{"version": 3, "file": "file-permissions.js", "sourceRoot": "", "sources": ["../../src/rules/file-permissions.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAA8F;AAE9F,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;AAEpF,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,SAAS,mBAAmB,CAAC,IAA2B;YACtD,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YACxB,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;gBACtC,OAAO,KAAK,CAAC;aACd;YACD,uEAAuE;YACvE,OAAO,oBAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,iBAAiB,CAAC,CAAC;QAC7D,CAAC;QAED,SAAS,eAAe,CAAC,QAAwB;YAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;YACjC,IAAI,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;gBACjC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;aACtC;iBAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;gBACxC,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;gBACzB,6FAA6F;gBAC7F,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;oBACvD,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;iBAChC;qBAAM;oBACL,IAAI,GAAG,SAAS,CAAC;iBAClB;aACF;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,+GAA+G;QAC/G,MAAM,QAAQ,GAA2B;YACvC,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,GAAG;YACZ,OAAO,EAAE,GAAG;YACZ,OAAO,EAAE,GAAG;YACZ,OAAO,EAAE,GAAG;SACb,CAAC;QAEF,SAAS,wBAAwB,CAAC,QAAiC;YACjE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC;YACtC,IAAI,0BAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,EAAE;gBACnF,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aAChC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,kBAAkB,CACzB,IAA6B,EAC7B,OAAyB;YAEzB,IAAI,CAAC,IAAI,EAAE;gBACT,OAAO,IAAI,CAAC;aACb;YACD,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE;gBACpC,OAAO,wBAAwB,CAAC,IAAI,CAAC,CAAC;aACvC;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;gBAClC,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC;aAC9B;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;gBACrC,MAAM,KAAK,GAAG,2BAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBAChC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACnB,OAAO,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;iBAC3C;aACF;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE;gBAC3C,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBACvC,IAAI,QAAQ,KAAK,GAAG,EAAE;oBACpB,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBACpD,MAAM,UAAU,GAAG,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBACtD,IAAI,SAAS,IAAI,UAAU,EAAE;wBAC3B,OAAO,SAAS,GAAG,UAAU,CAAC;qBAC/B;iBACF;aACF;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,iBAAiB,CAAC,IAAiB,EAAE,UAAkB;YAC9D,MAAM,OAAO,GAAG,IAAI,GAAG,EAAe,CAAC;YACvC,MAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/C,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,UAAU,EAAE;gBAC5D,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,OAAO,EAAE,oCAAoC;iBAC9C,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,cAAc,GAAG,IAA6B,CAAC;gBACrD,IAAI,mBAAmB,CAAC,cAAc,CAAC,EAAE;oBACvC,iBAAiB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAClD,iBAAiB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACnD;qBAAM,IAAI,mBAAW,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE;oBACnE,iBAAiB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACnD;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}