{"version": 3, "file": "no-gratuitous-expressions.js", "sourceRoot": "", "sources": ["../../src/rules/no-gratuitous-expressions.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,iEAA+F;AAGlF,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IAED,MAAM,CAAC,OAAyB;QAC9B,MAAM,SAAS,GAA6C,IAAI,GAAG,EAAE,CAAC;QACtE,MAAM,QAAQ,GAA6C,IAAI,GAAG,EAAE,CAAC;QAErE,OAAO;YACL,WAAW,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACjC,MAAM,IAAI,GAAI,IAA2B,CAAC,IAAI,CAAC;gBAC/C,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;oBAC9D,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC9C;YACH,CAAC;YAED,YAAY,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAClC,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;gBAClC,IAAI,MAAM,IAAI,qBAAa,CAAC,MAAM,CAAC,EAAE;oBACnC,6GAA6G;oBAC7G,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;oBAExC,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,EAAE;wBAC9B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBAC/D,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,kBAAkB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;wBAC3E,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,kBAAkB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;qBAC1E;yBAAM,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,oBAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;wBACjE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;qBACjF;iBACF;YACH,CAAC;YAED,iBAAiB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACvC,MAAM,IAAI,GAAG,IAAwB,CAAC;gBACtC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACvB,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YAED,UAAU,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAChC,MAAM,EAAE,GAAG,IAAyB,CAAC;gBACrC,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACjD,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;gBAClC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE;oBACtB,OAAO;iBACR;gBACD,IACE,CAAC,YAAY,CAAC,MAAM,CAAC;oBACrB,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC;oBAC3B,CAAC,qBAAa,CAAC,MAAM,CAAC;oBACtB,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAC1B;oBACA,OAAO;iBACR;gBAED,MAAM,qBAAqB,GAAG,CAC5B,GAA6C,EAC7C,MAAe,EACf,EAAE;oBACF,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;wBACvB,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;wBAC5D,IAAI,GAAG,EAAE;4BACP,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;yBAClC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC;gBAEF,qBAAqB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBACvC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YACzC,CAAC;YAED,OAAO,EAAE,GAAG,EAAE;gBACZ,SAAS,CAAC,KAAK,EAAE,CAAC;gBAClB,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,uBAAuB,CAAC,UAA6B;IAC5D,MAAM,MAAM,GAAwB,EAAE,CAAC;IACvC,MAAM,KAAK,GAAwB,EAAE,CAAC;IAEtC,MAAM,SAAS,GAAG,CAAC,IAAuB,EAAE,EAAE;QAC5C,IAAI,oBAAY,CAAC,IAAI,CAAC,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACnB;aAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;YAClC,IAAI,oBAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC/B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC3B;iBAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,oBAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACnF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;aACrC;SACF;IACH,CAAC,CAAC;IAEF,IAAI,OAAO,GAAG,UAAU,CAAC;IACzB,SAAS,CAAC,OAAO,CAAC,CAAC;IACnB,OAAO,YAAY,CAAC,OAAO,CAAC,EAAE;QAC5B,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;KACxB;IACD,SAAS,CAAC,OAAO,CAAC,CAAC;IAEnB,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC3B,CAAC;AAED,SAAS,YAAY,CAAC,UAAuB;IAC3C,OAAO,UAAU,CAAC,IAAI,KAAK,mBAAmB,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC;AACjF,CAAC;AAED,SAAS,cAAc,CACrB,EAAqB,EACrB,UAAuB;IAEvB,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,mBAAmB;QACvC,UAAU,CAAC,QAAQ,KAAK,IAAI;QAC5B,UAAU,CAAC,IAAI,KAAK,EAAE,CACvB,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAuB;IAChD,OAAO,UAAU,CAAC,IAAI,KAAK,iBAAiB,IAAI,UAAU,CAAC,QAAQ,KAAK,GAAG,CAAC;AAC9E,CAAC;AAED,SAAS,SAAS,CAAI,CAAuB;IAC3C,OAAO,CAAC,IAAI,IAAI,CAAC;AACnB,CAAC;AAED,SAAS,SAAS,CAAC,EAAqB,EAAE,KAAkB;IAC1D,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC;IAC5D,IAAI,GAAG,EAAE;QACP,OAAO,GAAG,CAAC,QAAQ,CAAC;KACrB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAkB;IAC1C,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;QACvB,OAAO,IAAI,CAAC;KACb;IACD,OAAO,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,cAAc,CAAC,MAAsB,EAAE,YAAyB;IACvE,OAAO,MAAM,CAAC,UAAU;SACrB,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;SAC5B,IAAI,CAAC,GAAG,CAAC,EAAE;QACV,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC;QAE1B,IAAI,GAAG,GAAuB,QAAQ,CAAC;QACvC,OAAO,GAAG,EAAE;YACV,IAAI,GAAG,KAAK,YAAY,EAAE;gBACxB,OAAO,IAAI,CAAC;aACb;YACD,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC;SACjB;QAED,MAAM,WAAW,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC3C,OAAO,OAAO,KAAK,WAAW,CAAC;IACjC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAwB,EAAE,YAAyB;IAC7E,OAAO,GAAG;SACP,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,KAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC;SACxE,MAAM,CAAC,SAAS,CAAC;SACjB,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACtC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,QAAS,EAAE,YAAY,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,MAAM,CACb,EAAe,EACf,GAAgC,EAChC,OAAyB,EACzB,MAAe;IAEf,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;IAE1C,MAAM,cAAc,GAAmB;QACrC,OAAO,EAAE,4BAA4B,KAAK,mCAAmC;QAC7E,kBAAkB,EAAE,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC;KACtD,CAAC;IAEF,OAAO,CAAC,MAAM,CAAC;QACb,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;QACvC,IAAI,EAAE,EAAE;KACT,CAAC,CAAC;AACL,CAAC;AAED,SAAS,qBAAqB,CAAC,GAAgC,EAAE,MAAc;IAC7E,IAAI,GAAG,EAAE;QACP,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,GAAI,CAAC;QACnC,OAAO;YACL;gBACE,OAAO,EAAE,wBAAwB,MAAM,EAAE;gBACzC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;gBACvB,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM;gBAC3B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI;gBACxB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;aAC7B;SACF,CAAC;KACH;SAAM;QACL,OAAO,EAAE,CAAC;KACX;AACH,CAAC"}