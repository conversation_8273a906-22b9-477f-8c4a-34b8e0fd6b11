{"version": 3, "file": "elseif-without-else.js", "sourceRoot": "", "sources": ["../../src/rules/elseif-without-else.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,gDAAgD;;;AAIhD,iEAAkE;AAErD,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,WAAW,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACjC,MAAM,MAAM,GAAG,IAA0B,CAAC;gBAC1C,IAAI,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;oBAClD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;oBAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,CAC3C,IAAI,EACJ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,CAC5D,CAAC;oBACF,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CACxC,IAAI,EACJ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAC1D,CAAC;oBACF,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,gCAAgC;wBACzC,GAAG,EAAE;4BACH,KAAK,EAAE,WAAY,CAAC,GAAG,CAAC,KAAK;4BAC7B,GAAG,EAAE,SAAU,CAAC,GAAG,CAAC,GAAG;yBACxB;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,QAAQ,CAAC,IAAwB,EAAE,OAAyB;IACnE,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC;AAC9E,CAAC"}