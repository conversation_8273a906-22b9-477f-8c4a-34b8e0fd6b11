{"version": 3, "file": "no-try-promise.js", "sourceRoot": "", "sources": ["../../src/rules/no-try-promise.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAKjD,+CAAiC;AACjC,oCAA8F;AAOjF,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACtC,OAAO;gBACL,YAAY,EAAE,CAAC,IAAiB,EAAE,EAAE,CAClC,iBAAiB,CAAC,IAA6B,EAAE,OAAO,EAAE,QAAQ,CAAC;aACtE,CAAC;SACH;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAC;AAEF,SAAS,iBAAiB,CACxB,OAA8B,EAC9B,OAAyB,EACzB,QAAa;IAEb,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,qBAAqB;QACrB,MAAM,YAAY,GAAoB,EAAE,CAAC;QACzC,kBAAkB;QAClB,MAAM,gBAAgB,GAAoB,EAAE,CAAC;QAE7C,IAAI,2BAA2B,GAAG,KAAK,CAAC;QACxC,yBAAyB,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC1F,IAAI,YAAY,CAAC,IAAI,KAAK,iBAAiB,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE;gBACrF,2BAA2B,GAAG,IAAI,CAAC;gBACnC,OAAO;aACR;YAED,IACE,CAAC,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,KAAK,iBAAiB,CAAC;gBACvE,QAAQ,CAAC,YAAY,CAAC;gBACtB,OAAO,CAAC,YAAY,CAAC,EACrB;gBACA,OAAO;aACR;YAED,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,EAAE;YAChC,kBAAkB,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YACnD,oBAAoB,CAAC,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;SACxE;KACF;AACH,CAAC;AAED,MAAM,yBAAyB;IAA/B;QACmB,wBAAmB,GAAyB,EAAE,CAAC;IAyBlE,CAAC;IAvBC,MAAM,CAAC,kBAAkB,CAAC,IAAmB,EAAE,OAAyB;QACtE,MAAM,OAAO,GAAG,IAAI,yBAAyB,EAAE,CAAC;QAChD,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7B,OAAO,OAAO,CAAC,mBAAmB,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,IAAmB,EAAE,OAAyB;QAC1D,MAAM,SAAS,GAAG,CAAC,IAAmB,EAAE,EAAE;YACxC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,eAAe;oBAClB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACpC,MAAM;gBACR,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,oBAAoB,CAAC;gBAC1B,KAAK,yBAAyB;oBAC5B,OAAO;aACV;YACD,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC3E,CAAC,CAAC;QACF,SAAS,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;CACF;AAED,SAAS,kBAAkB,CACzB,OAA8B,EAC9B,YAA6B,EAC7B,OAAyB;IAEzB,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3B,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,MAAM,OAAO,GAAG,yCAAyC,MAAM,6EAA6E,MAAM,GAAG,CAAC;QACtJ,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,OAAsB,CAAC,CAAC;QAC5E,OAAO,CAAC,MAAM,CAAC;YACb,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5F,GAAG,EAAE,KAAM,CAAC,GAAG;SAChB,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,oBAAoB,CAC3B,OAA8B,EAC9B,YAA6B,EAC7B,gBAAiC,EACjC,OAAyB;IAEzB,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5D,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACtD,MAAM,OAAO,GAAG,oDAAoD,MAAM,sDAAsD,CAAC;QACjI,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,OAAsB,CAAC,CAAC;QAC5E,OAAO,CAAC,MAAM,CAAC;YACb,OAAO,EAAE,wBAAgB,CACvB,OAAO,EACP,gBAAgB,EAChB,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CACtD;YACD,GAAG,EAAE,KAAM,CAAC,GAAG;SAChB,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,aAAa,CAAC,IAAmB,EAAE,QAAgC;IAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxD,MAAM,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACvE,MAAM,YAAY,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC5C,OAAO,OAAO,CAAC,YAAY,IAAI,YAAY,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC7E,CAAC;AAED,SAAS,QAAQ,CAAC,QAA4B;IAC5C,OAAO,CACL,QAAQ,CAAC,MAAM;QACf,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB;QAC3C,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;QAC9C,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,MAAM,CACzC,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,QAA4B;IAC5C,OAAO,CACL,QAAQ,CAAC,MAAM;QACf,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB;QAC3C,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;QAC9C,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC1C,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,QAA4B;IAC3C,OAAO,CACL,QAAQ,CAAC,IAAI,KAAK,gBAAgB;QAClC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB;QAC3C,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;QAC9C,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC1C,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,IAAmB,EAAE,WAAmC;IAC1E,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,EAAE;QACR,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,MAAM,KAAK,GAAI,IAAY,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;aACzB;iBAAM;gBACL,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACtB;SACF;KACF;IACD,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAClC,CAAC"}