{"version": 3, "file": "index-of-compare-to-positive-number.js", "sourceRoot": "", "sources": ["../../src/rules/index-of-compare-to-positive-number.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,oCAAqF;AAErF,MAAM,OAAO,GACX,oGAAoG,CAAC;AAE1F,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QACD,OAAO;YACL,gBAAgB,CAAC,IAAiB;gBAChC,MAAM,UAAU,GAAG,IAA+B,CAAC;gBACnD,IACE,UAAU,CAAC,QAAQ,KAAK,GAAG;oBAC3B,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;oBACxB,kBAAkB,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,EAC7C;oBACA,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;iBACnC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,MAAM,CAAC,IAAuB;IACrC,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAuB,EAAE,QAAgC;IACnF,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,gBAAgB;QAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB;QACvC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;QAC1C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;QACvC,eAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CACtC,CAAC;AACJ,CAAC"}