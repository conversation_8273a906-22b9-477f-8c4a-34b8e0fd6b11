{"version": 3, "file": "updated-loop-counter.js", "sourceRoot": "", "sources": ["../../src/rules/updated-loop-counter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,iEAAkE;AAElE,oCAAqF;AAExE,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IAED,MAAM,CAAC,OAAyB;QAC9B,SAAS,SAAS,CAChB,UAAa,EACb,eAAuE,EACvE,QAAqB;YAErB,MAAM,QAAQ,GAAwB,EAAE,CAAC;YACzC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACtC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,QAAiC,CAAC,CAAC,CAAC;QACxF,CAAC;QAED,SAAS,YAAY,CAAC,OAA0B,EAAE,KAAkB;YAClE,MAAM,QAAQ,GAAG,2BAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5D,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO;aACR;YACD,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAChC,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE;oBAC5D,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,GAAG,CAAC,UAAU;wBACpB,OAAO,EAAE,wBAAgB,CACvB,8BAA8B,OAAO,CAAC,IAAI,IAAI,EAC9C,CAAC,OAAwB,CAAC,EAC1B,CAAC,yBAAyB,CAAC,CAC5B;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,+BAA+B,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACrD,MAAM,OAAO,GAAG,iBAAS,CAAC,OAAO,CAAwB,CAAC;gBAC1D,IAAI,OAAO,CAAC,MAAM,EAAE;oBAClB,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;iBACrD;YACH,CAAC;YACD,kEAAkE,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACxF,MAAM,EAAE,IAAI,EAAE,GAAG,iBAAS,CAAC,OAAO,CAAkD,CAAC;gBACrF,SAAS,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;YAC7C,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,mBAAmB,CAC1B,gBAA6D,EAC7D,QAA6B;IAE7B,IAAI,gBAAgB,CAAC,IAAI,KAAK,qBAAqB,EAAE;QACnD,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;KACvF;SAAM;QACL,0BAAkB,CAAC,gBAAiC,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;KAC9F;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,gBAAmC,EAAE,QAA6B;IAC5F,IAAI,OAAO,GAAmC,SAAS,CAAC;IAExD,IAAI,gBAAgB,CAAC,IAAI,KAAK,sBAAsB,EAAE;QACpD,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC;KACjC;SAAM,IAAI,gBAAgB,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACvD,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC;KACrC;SAAM,IAAI,gBAAgB,CAAC,IAAI,KAAK,oBAAoB,EAAE;QACzD,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC5E;IAED,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,YAAY,EAAE;QAC5C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACxB;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,EAAqB,EAAE,QAAqB;IACpE,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;IACjC,OAAO,EAAE,CAAC,KAAK,IAAI,SAAS,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC3F,CAAC"}