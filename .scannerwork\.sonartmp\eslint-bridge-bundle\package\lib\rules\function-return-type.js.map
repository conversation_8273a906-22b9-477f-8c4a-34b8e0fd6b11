{"version": 3, "file": "function-return-type.js", "sourceRoot": "", "sources": ["../../src/rules/function-return-type.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;AAIjD,yEAAyF;AACzF,iEAAkE;AAElE,+CAAiC;AACjC,oCAAkG;AAElG,MAAM,aAAa;IAAnB;QACmB,qBAAgB,GAA6B,EAAE,CAAC;IASnE,CAAC;IAPC,mBAAmB;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;IAED,kBAAkB,CAAC,IAA4B;QAC7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;CACF;AAEY,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,IAAI,MAAM,GAAoB,EAAE,CAAC;QAEjC,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,cAAc,CAAC,IAAiB;YACvC,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,EAAG,CAAC,mBAAmB,EAAE,CAAC;YAC7D,IAAI,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,WAAC,OAAA,CAAA,MAAA,OAAO,CAAC,QAAQ,0CAAE,IAAI,MAAK,gBAAgB,CAAA,EAAA,CAAC,EAAE;gBAClF,OAAO;aACR;YACD,MAAM,SAAS,GAAG,OAAO,CAAC,2BAA2B,CACnD,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAqB,CAA4B,CACrF,CAAC;YACF,IAAI,SAAS,IAAI,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;gBAC3D,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CACnC,OAAO,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,2BAAmB,CAAC,OAAO,CAAC,QAAS,EAAE,QAAQ,CAAC,CAAC,CACzE,CAAC;gBACF,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,2BAAmB,CAAC,OAAO,CAAC,QAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC1F,IAAI,UAAU,CAAC,KAAK,CAAC,aAAK,CAAC,EAAE;oBAC3B,OAAO;iBACR;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,OAAO,EAAE,wBAAgB,CACvB,wDAAwD,EACxD,KAAK,EACL,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,CACxE;oBACD,GAAG,EAAE,wCAA4B,CAAC,IAAuB,EAAE,iBAAS,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;iBACxF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,eAAe,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACrC,MAAM,OAAO,GAAG,IAA8B,CAAC;gBAC/C,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACzC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;iBACvD;YACH,CAAC;YACD,WAAW,EAAE,GAAG,EAAE;gBAChB,MAAM,CAAC,IAAI,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC;YACnC,CAAC;YACD,gBAAgB,EAAE,cAAc;YAChC,cAAc,EAAE,GAAG,EAAE;gBACnB,MAAM,GAAG,EAAE,CAAC;YACd,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,sBAAsB,CAAC,SAAuB,EAAE,OAAuB;IAC9E,MAAM,UAAU,GAAG,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC,CAAC;IACjG,OAAO,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACxE,CAAC;AAED,SAAS,OAAO,CAAC,IAAa,EAAE,OAAuB;IACrD,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,KAAa,EAAE,IAAc,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;IACjG,MAAM,SAAS,GAAG,CAAC,EAAW,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,aAAa,GAAG,CAAC,EAAW,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IACvD,OAAO,CACL,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAC9F,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAuB;IACjD,OAAO,SAAS,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACxF,CAAC;AAED,SAAS,WAAW,CAAC,IAAa,EAAE,OAAuB;IACzD,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,KAAa,EAAE,IAAc,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;IACjG,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;QAC3E,OAAO,QAAQ,CAAC;KACjB;IACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;QAChC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD,OAAO,IAAI,CAAC,KAAK;aACd,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aACnC,MAAM,CAAC,QAAQ,CAAC;aAChB,IAAI,CAAC,SAAS,CAAC,CAAC;KACpB;IACD,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IACpE,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,IAAI,EAAE,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACnC,OAAO,UAAU,CAAC;SACnB;QACD,IAAI,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE;YAChC,OAAO,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SAC7C;KACF;IACD,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;AACtE,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAsB,EAAE,OAAuB;IACxE,IAAI,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC;IACtF,uFAAuF;IACvF,iDAAiD;IACjD,IAAI,WAAW,KAAK,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE;QAC/E,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YAC7B,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,WAAW,GAAG,QAAQ,CAAC;gBACvB,MAAM;YACR,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,WAAW,GAAG,QAAQ,CAAC;gBACvB,MAAM;YACR,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;gBAC/B,WAAW,GAAG,SAAS,CAAC;gBACxB,MAAM;YACR,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;gBAC5B,WAAW,GAAG,QAAQ,CAAC;gBACvB,MAAM;SACT;KACF;IACD,OAAO,GAAG,WAAW,IAAI,CAAC;AAC5B,CAAC;AAED,SAAS,UAAU,CAAC,IAAa;IAC/B,OAAO,CACL,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;QACtC,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;QACtC,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAC5C,CAAC;AACJ,CAAC"}