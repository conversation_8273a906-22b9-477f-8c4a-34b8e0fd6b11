{"version": 3, "file": "function-inside-loop.js", "sourceRoot": "", "sources": ["../../src/rules/function-inside-loop.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAIjD,iEAAkE;AAClE,yEAAyF;AAEzF,oCAAiF;AAEjF,MAAM,OAAO,GAAG,iEAAiE,CAAC;AAElF,MAAM,QAAQ,GAAG,4EAA4E,CAAC;AAE9F,MAAM,YAAY,GAAG,gEAAgE,CAAC;AAEtF,MAAM,gBAAgB,GAAG;IACvB,SAAS;IACT,SAAS;IACT,QAAQ;IACR,KAAK;IACL,MAAM;IACN,WAAW;IACX,OAAO;IACP,MAAM;IACN,QAAQ;IACR,aAAa;IACb,MAAM;IACN,MAAM;CACP,CAAC;AAEW,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAyB;QAC9B,SAAS,qBAAqB,CAAC,IAAiB;YAC9C,OAAO,iCAAyB,CAAC,IAAqB,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO;YACL,CAAC,YAAY,CAAC,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,QAAQ,GAAG,qBAAqB,CAAC,IAAI,CAAa,CAAC;gBACzD,IAAI,QAAQ,EAAE;oBACZ,IACE,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC;wBACtB,CAAC,kBAAkB,CAAC,OAAO,CAAC;wBAC5B,OAAO,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,EAC9D;wBACA,OAAO,CAAC,MAAM,CAAC;4BACb,OAAO,EAAE,wBAAgB,CAAC,OAAO,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;4BACzE,GAAG,EAAE,wCAA4B,CAC/B,IAAuB,EACvB,iBAAS,CAAC,OAAO,CAAC,EAClB,OAAO,CACR;yBACF,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,MAAM,CAAC,IAAiB,EAAE,OAAyB;IAC1D,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,CACL,MAAM;QACN,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,gBAAgB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;YAC3D,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAClE,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAyB;IACnD,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE;QAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;YACtC,OAAO,CACL,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY,IAAI,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CACzF,CAAC;SACH;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAC,GAAoB,EAAE,QAAkB;IACtD,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC9B,IAAI,QAAQ,EAAE;QACZ,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,WAAW,GAAG,UAAU,IAAI,UAAU,CAAC,MAAM,CAAC;QACpD,MAAM,IAAI,GAAG,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,qBAAqB,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/F,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,OAAO,EAAE;YACtC,OAAO,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC1C;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CAAC,QAAwB,EAAE,QAAkB;IACjE,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,UAAU,EAAE;QACrC,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE;YACjB,4CAA4C;YAC5C,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,IAAI,EAAE;gBACjE,OAAO,KAAK,CAAC;aACd;YAED,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC;YACtC,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACzC,iDAAiD;YACjD,IAAI,QAAQ,IAAI,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC3E,OAAO,KAAK,CAAC;aACd;SACF;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAkB;IAC1C,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;IACtC,IAAI,SAAS,EAAE;QACb,QAAQ,QAAQ,CAAC,IAAI,EAAE;YACrB,KAAK,cAAc;gBACjB,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;oBACxC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC/C;gBACD,MAAM;YACR,KAAK,gBAAgB,CAAC;YACtB,KAAK,kBAAkB;gBACrB,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7B,KAAK,gBAAgB,CAAC;YACtB,KAAK,gBAAgB;gBACnB,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;gBACjC,IAAI,SAAS,EAAE;oBACb,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;iBACrC;SACJ;KACF;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAc,EAAE,OAAyB;IACjE,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAC3C,IAAI,KAAuB,CAAC;IAC5B,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,gBAAgB,CAAC;QACtB,KAAK,kBAAkB;YACrB,KAAK,GAAG,UAAU,CAAC,cAAc,CAC/B,IAAI,CAAC,IAAI,EACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,KAAK,OAAO,CACjD,CAAC;YACF,MAAM;QACR,KAAK,cAAc,CAAC;QACpB,KAAK,gBAAgB,CAAC;QACtB;YACE,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;KAC1F;IACD,OAAO,KAAM,CAAC;AAChB,CAAC"}