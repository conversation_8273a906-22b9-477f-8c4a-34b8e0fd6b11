{"version": 3, "file": "unused-import.js", "sourceRoot": "", "sources": ["../../src/rules/unused-import.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;AAMjD,MAAM,gBAAgB,GAAG,CAAC,OAAO,CAAC,CAAC;AAEtB,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,cAAc,GAClB,OAAO;aACJ,aAAa,EAAE;aACf,cAAc,EAAE;aAChB,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnE,MAAM,aAAa,GAAwB,EAAE,CAAC;QAC9C,MAAM,iBAAiB,GAAgB,IAAI,GAAG,EAAE,CAAC;QACjD,MAAM,kBAAkB,GAAG,CAAC,IAAuB,EAAE,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzF,SAAS,UAAU,CAAC,QAAwB;YAC1C,OAAO,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,SAAS,QAAQ,CAAC,QAAwB;YACxC,OAAO,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;QAC1C,CAAC;QAED,SAAS,aAAa,CAAC,QAAwB;YAC7C,OAAO,QAAQ,CAAC,IAAI,KAAK,KAAK,IAAI,cAAc,CAAC;QACnD,CAAC;QAED,OAAO;YACL,iBAAiB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACvC,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBACrD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;oBAChC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;wBAC3E,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC7C;iBACF;YACH,CAAC;YACD,gGAAgG,EAAE,CAChG,IAAiB,EACjB,EAAE;gBACF,kBAAkB,CAAC,IAAyB,CAAC,CAAC;YAChD,CAAC;YACD,2CAA2C,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACjE,kBAAkB,CAAG,IAAyC,CAAC,IAAyB,CAAC,CAAC;YAC5F,CAAC;YACD,sIAAsI,EAAE,CACtI,IAAiB,EACjB,EAAE;gBACF,kBAAkB,CACd,IAA0C,CAAC,MAA2B,CACzE,CAAC;YACJ,CAAC;YACD,cAAc,EAAE,GAAG,EAAE;gBACnB,MAAM,cAAc,GAAG,OAAO;qBAC3B,aAAa,EAAE;qBACf,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC;qBAC1D,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC7B,aAAa;qBACV,MAAM,CACL,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CACvF;qBACA,OAAO,CAAC,MAAM,CAAC,EAAE,CAChB,OAAO,CAAC,MAAM,CAAC;oBACb,OAAO,EAAE,iCAAiC,MAAM,CAAC,IAAI,IAAI;oBACzD,IAAI,EAAE,MAAM;iBACb,CAAC,CACH,CAAC;YACN,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC"}