{"version": 3, "file": "no-implicit-dependencies.js", "sourceRoot": "", "sources": ["../../src/rules/no-implicit-dependencies.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;;;;;;;;;;;;;;;;;;;;AAIjD,sEAAuC;AACvC,2CAA6B;AAC7B,uCAAyB;AACzB,+CAAiC;AAGjC,MAAM,eAAe,GAAG,SAAS,CAAC;AAElC;;GAEG;AACH,MAAM,KAAK,GAA6B,IAAI,GAAG,EAAE,CAAC;AAErC,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC;QAClC,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QAC5D,MAAM,2BAA2B,GAAG,0BAA0B,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACvF,IAAI,2BAA2B,KAAK,UAAU,EAAE;YAC9C,oCAAoC;YACpC,OAAO,EAAE,CAAC;SACX;QACD,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,IAAI,GAAG,IAA6B,CAAC;gBAC3C,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,YAAY;oBACjC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS;oBAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAC3B;oBACA,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;oBAClC,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;wBAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;wBACjC,qBAAqB,CACnB,QAAQ,EACR,YAAY,CAAC,GAAI,EACjB,YAAY,EACZ,SAAS,EACT,2BAA2B,EAC3B,OAAO,CACR,CAAC;qBACH;iBACF;YACH,CAAC;YACD,iBAAiB,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACvC,MAAM,MAAM,GAAI,IAAiC,CAAC,MAAM,CAAC;gBACzD,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAChE,qBAAqB,CACnB,MAAM,EACN,WAAY,CAAC,GAAG,EAChB,YAAY,EACZ,SAAS,EACT,2BAA2B,EAC3B,OAAO,CACR,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,qBAAqB,CAC5B,MAAsB,EACtB,GAA0B,EAC1B,YAAyB,EACzB,SAAmB,EACnB,2BAAiD,EACjD,OAAyB;IAEzB,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC;IAChC,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,OAAO;KACR;IAED,IAAI,EAAE,CAAC,4BAA4B,CAAC,UAAU,CAAC,EAAE;QAC/C,OAAO;KACR;IAED,IAAI,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,EAAE;QACnF,OAAO;KACR;IAED,MAAM,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;IAC/C,IACE,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC;QAChC,CAAC,yBAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC/B,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAC9B;QACA,OAAO,CAAC,MAAM,CAAC;YACb,OAAO,EAAE,sDAAsD;YAC/D,GAAG;SACJ,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,cAAc,CAAC,IAAY;IAClC;;;MAGE;IACF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACzB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;KACjB;SAAM;QACL,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;KAClC;AACH,CAAC;AAED,SAAS,eAAe,CAAC,QAAgB;IACvC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAEvC,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,MAAM,EAAE;QACV,OAAO,MAAM,CAAC;KACf;IAED,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;IACjC,MAAM,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/D,IAAI,eAAe,KAAK,SAAS,EAAE;QACjC,IAAI;YACF,8CAA8C;YAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;YAC5F,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;gBACtC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;aAC/C;YACD,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;gBACzC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;aAClD;YACD,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;gBAC1C,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC;aACnD;SACF;QAAC,WAAM,GAAE;KACX;IAED,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAE3B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,eAAe,CAAC,MAAmB,EAAE,YAAiB;IAC7D,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CACvC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAC7F,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,OAAe;IACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IACpD,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC3B,OAAO,QAAQ,CAAC;KACjB;IAED,MAAM,IAAI,GAAW,OAAO,CAAC;IAC7B,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAEhC,IAAI,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC;KACjC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAUD,MAAM,4BAA4B;IAChC,YAA6B,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;IAAG,CAAC;IAC9C,cAAc,CAAC,IAAY;QACzB,OAAO,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC;IAC7B,CAAC;CACF;AAED,MAAM,gCAAgC;IACpC,YAA6B,MAAc,EAAmB,MAAc;QAA/C,WAAM,GAAN,MAAM,CAAQ;QAAmB,WAAM,GAAN,MAAM,CAAQ;IAAG,CAAC;IAChF,cAAc,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpE,CAAC;CACF;AAED,MAAM,6BAA6B,GAAG,oBAAoB,CAAC,CAAC,8CAA8C;AAC1G,MAAM,wCAAwC,GAAG,CAAC,CAAC;AACnD,MAAM,wCAAwC,GAAG,CAAC,CAAC;AACnD,SAAS,0BAA0B,CACjC,cAAsC;IAEtC,MAAM,eAAe,GAAG,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;IAC9F,MAAM,KAAK,GAAG,CAAC,eAAe,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IAC/D,MAAM,mBAAmB,GAAyB,EAAE,CAAC;IACrD,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;QACrB,IAAI,CAAC,KAAK,GAAG,EAAE;YACb,OAAO,UAAU,CAAC;SACnB;aAAM;YACL,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,IAAI,CAAC,EAAE;gBACL,mBAAmB,CAAC,IAAI,CACtB,IAAI,gCAAgC,CAClC,CAAC,CAAC,wCAAwC,CAAC,EAC3C,CAAC,CAAC,wCAAwC,CAAC,CAC5C,CACF,CAAC;aACH;iBAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC3B,mBAAmB,CAAC,IAAI,CAAC,IAAI,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/D;iBAAM;gBACL,mFAAmF;aACpF;SACF;KACF;IACD,OAAO,mBAAmB,CAAC;AAC7B,CAAC"}