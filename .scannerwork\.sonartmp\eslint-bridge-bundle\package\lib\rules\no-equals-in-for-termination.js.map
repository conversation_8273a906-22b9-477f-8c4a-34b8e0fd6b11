{"version": 3, "file": "no-equals-in-for-termination.js", "sourceRoot": "", "sources": ["../../src/rules/no-equals-in-for-termination.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,gDAAgD;;;AAIhD,oCAA+C;AAE/C,MAAM,gBAAgB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtC,MAAM,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAU1B,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,OAAO;YACL,YAAY,EAAE,CAAC,IAAiB,EAAE,EAAE;gBAClC,MAAM,YAAY,GAAG,IAA2B,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;oBAC9C,OAAO;iBACR;gBACD,MAAM,oBAAoB,GAAG,IAA4B,CAAC;gBAC1D,MAAM,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC;gBAC5C,IACE,UAAU,CAAC,SAAS,CAAC;oBACrB,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC;oBAC3C,CAAC,WAAW,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAC3C;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EACL,YAAY,SAAS,CAAC,QAAQ,kBAAkB;4BAChD,sDAAsD;wBACxD,IAAI,EAAE,SAAS;qBAChB,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,UAAU,CAAC,UAA6B;IAC/C,OAAO,CAAC,CAAC,CACP,UAAU,CAAC,IAAI,KAAK,kBAAkB,IAAI,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CACzF,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CAAC,UAA6B;IACnD,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,KAAK,kBAAkB,EAAE;QAClE,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,UAAU,CAAC,IAAI,KAAK,oBAAoB,EAAE;QACnD,OAAO,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;KACrD;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,QAAQ,CAAC,UAA6B;IAC7C,OAAO,CAAC,CAAC,CACP,UAAU,CAAC,IAAI,KAAK,sBAAsB,IAAI,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAC9F,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,YAAkC,EAAE,OAAyB;IAChF,OAAO,CACL,8BAA8B,CAAC,YAAY,CAAC;QAC5C,0BAA0B,CAAC,YAAY,EAAE,OAAO,CAAC,CAClD,CAAC;AACJ,CAAC;AAED,SAAS,8BAA8B,CAAC,YAAkC;IACxE,+DAA+D;IAC/D,MAAM,SAAS,GAAG,YAAY,CAAC,IAA+B,CAAC;IAC/D,IAAI,QAAQ,GAAkB,EAAE,CAAC;IACjC,eAAe,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC/C,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzF,CAAC;AAED,SAAS,eAAe,CAAC,UAA6B,EAAE,QAAuB;IAC7E,IAAI,OAAO,GAAmC,SAAS,CAAC;IAExD,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxB,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;KAC3B;SAAM,IAAI,UAAU,CAAC,IAAI,KAAK,kBAAkB,EAAE;QACjD,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC;KAC/B;SAAM,IAAI,UAAU,CAAC,IAAI,KAAK,oBAAoB,EAAE;QACnD,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;KACnE;IAED,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,YAAY,EAAE;QAC5C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAC7B;AACH,CAAC;AAED,SAAS,0BAA0B,CAAC,YAAkC,EAAE,OAAyB;IAC/F,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;IAC/B,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC;IAEpC,IAAI,IAAI,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE;QACjC,MAAM,YAAY,GAAG,mBAAmB,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC1F,IAAI,YAAY,KAAK,CAAC,EAAE;YACtB,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;YACrC,OAAO,CACL,UAAU,KAAK,SAAS;gBACxB,QAAQ,KAAK,SAAS;gBACtB,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,CAClD,CAAC;SACH;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,UAAU,CAAC,IAAiB;IACnC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;AAChF,CAAC;AAED,SAAS,mBAAmB,CAC1B,MAAyB,EACzB,QAAqB,EACrB,OAAyB;IAEzB,IAAI,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE;QAC5C,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxD,OAAO,CAAC,CAAC,CAAC;SACX;QACD,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxD,OAAO,CAAC,CAAC,CAAC;SACX;KACF;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,aAAa,CACpB,MAAyB,EACzB,QAAqB,EACrB,OAAyB;IAEzB,OAAO,CACL,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC7F,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CACvF,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,EAAe,EAAE,QAAqB,EAAE,OAAyB;IACzF,IAAI,EAAE,CAAC,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM,QAAQ,GAAG,2BAAmB,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;QACjC,IAAI,QAAQ,IAAI,SAAS,EAAE;YACzB,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;SAC7E;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,QAAQ,CAAC,EAAqB,EAAE,SAA2B;IAClE,OAAO,EAAE,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC;AAED,SAAS,QAAQ,CAAC,IAAiB;IACjC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;QACpB,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC/B;SAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,EAAE;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KAClC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAsB,EAAE;QAC/C,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC/B;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,UAAU,CAAC,IAAoC;IACtD,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;QACrE,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAiB;IAC5C,OAAO,IAAI,CAAC,IAAI,KAAK,qBAAqB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAA6B;IAE7B,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxB,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;QAC/B,OAAO,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;KACtD;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}