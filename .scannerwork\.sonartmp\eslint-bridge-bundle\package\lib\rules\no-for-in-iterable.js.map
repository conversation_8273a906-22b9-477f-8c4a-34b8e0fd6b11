{"version": 3, "file": "no-for-in-iterable.js", "sourceRoot": "", "sources": ["../../src/rules/no-for-in-iterable.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,iDAAiD;;;;;;AAIjD,4DAA4B;AAC5B,oCAAyE;AAE5D,QAAA,IAAI,GAAoB;IACnC,MAAM,CAAC,OAAyB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,gCAAwB,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,EAAE,CAAC;SACX;QACD,OAAO;YACL,cAAc,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACpC,MAAM,IAAI,GAAG,2BAAmB,CAAE,IAA8B,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAClF,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;oBACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;oBAC3D,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,wCAAwC,QAAQ,IAAI;wBAC7D,GAAG,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,GAAG;qBACtD,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,UAAU,CAAC,IAAa;IAC/B,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,YAAY,CAAC,IAAa;IACjC,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,SAAS;QACzB;YACE,OAAO;YACP,WAAW;YACX,YAAY;YACZ,mBAAmB;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,aAAa;YACb,cAAc;YACd,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,KAAK;YACL,KAAK;SACN,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAC7B,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,IAAa;IAC7B,OAAO,CACL,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC;QAC5D,CAAC,IAAI,CAAC,KAAK,GAAG,oBAAE,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAC7C,CAAC;AACJ,CAAC"}