{"version": 3, "file": "sonar-max-lines-per-function.js", "sourceRoot": "", "sources": ["../../src/rules/sonar-max-lines-per-function.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,yIAAyI;AACzI,6HAA6H;;;AAM7H,yEAAyF;AACzF,iEAAkE;AAErD,QAAA,IAAI,GAAoB;IACnC,IAAI,EAAE;QACJ,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;KAC9B;IACD,MAAM,CAAC,OAAyB;QAC9B,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;QAEpC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;QAE/B,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;QAE9E,OAAO;YACL,kEAAkE,EAAE,CAAC,IAAiB,EAAE,EAAE;gBACxF,MAAM,MAAM,GAAG,iBAAS,CAAC,OAAO,CAAC,CAAC;gBAElC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,MAAqB,CAAC,EAAE;oBACpD,OAAO;iBACR;gBAED,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;gBAErE,IAAI,SAAS,GAAG,SAAS,EAAE;oBACzB,OAAO,CAAC,MAAM,CAAC;wBACb,OAAO,EAAE,qBAAqB,SAAS,qCAAqC,SAAS,qDAAqD;wBAC1I,GAAG,EAAE,wCAA4B,CAAC,IAAuB,EAAE,iBAAS,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;qBACxF,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAgB,aAAa,CAC3B,GAA0B,EAC1B,KAAe,EACf,kBAA+C;IAE/C,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;QACtD,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,MAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,OAAO,IAAI,iBAAiB,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE;YACtD,SAAS;SACV;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YACxB,SAAS;SACV;QAED,SAAS,EAAE,CAAC;KACb;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAtBD,sCAsBC;AAED,SAAgB,qBAAqB,CAAC,QAA0B;IAC9D,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;IAEtB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACnE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;aACrB;SACF;IACH,CAAC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC;AACb,CAAC;AAXD,sDAWC;AAED,SAAS,iBAAiB,CAAC,IAAY,EAAE,UAAkB,EAAE,OAAuB;IAClF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;QAChB,OAAO,KAAK,CAAC;KACd;IACD,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAC7B,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,EACrB,kBAAkB,GAAG,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EACrF,iBAAiB,GAAG,GAAG,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;IAEhF,OAAO,CACL,OAAO;QACP,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,IAAI,kBAAkB,CAAC;QAC/C,CAAC,GAAG,CAAC,IAAI,GAAG,UAAU,IAAI,iBAAiB,CAAC,CAC7C,CAAC;AACJ,CAAC;AAED,SAAS,MAAM,CAAC,IAAiB,EAAE,MAAmB;IACpD,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,oBAAoB;QAClC,MAAM;QACN,MAAM,CAAC,IAAI,KAAK,gBAAgB;QAChC,MAAM,CAAC,MAAM,KAAK,IAAI,CACvB,CAAC;AACJ,CAAC"}